import {TreeNode} from 'primeng/api';
import {
  areAllLoaded,
  createFolderN<PERSON>,
  createLoadMoreNode,
  createNode,
  FolderData,
  FileData,
  FileSystemEntry
} from './FileSystemEntry';
import {getPathSegments, toPath} from '../shared/util/path-utils';
import { FileDataWithPath } from '../client/client';

export class FileSystemView {
  root: TreeNode<FileSystemEntry>;
  constructor(data: FolderData, private readonly autoExpandAll: boolean = false) {
    this.root = createFolderNode({
      name: data.name,
      totalElementCount: data.totalElementCount ?? 0,
      currentPage: 1,
      pageSize: 10,
      loadedElementCount: 0,
      children: [],
    });
    if (autoExpandAll) {
      this.root.expanded = true;
    }
  }
  get rootAsFolder() {
    return this.root as TreeNode<FolderData>;
  }

  static fromFolderData(data: FolderData): FileSystemView {
    return new FileSystemView(data);
  }

  static fromFilesWithPaths(files: FileDataWithPath[]): FileSystemView {
    const view = new FileSystemView({
      name: 'Search Results',
      totalElementCount: files.length,
      type: 'folder',
      loadedElementCount: 0,
      currentPage: 1,
      pageSize: files.length
    }, true);
    for (const result of files) {
      const pathSegments = getPathSegments(result.path!);
      const fileName = pathSegments.pop() || '';
      view.addFileToPath(pathSegments, {
        type: 'pdf' as const,
        name: fileName,
        size: result.size,
        isCrossed: false,
        isRed: false,
        hasNotification: false
      });
    }

    return view;
  }

  get allFiles(): TreeNode<FileData>[] {
    return this
      .flattenLeaves(this.root.children ?? [])
      .concat(this.root.children ?? [])
      .filter(n => n.data?.type !== "folder") as TreeNode<FileData>[]
  }

  addFileToPath(pathSegments: string[], fileEntry: FileData): void {
    let currentPath = '';
    for (const segment of pathSegments.filter(s => s !== '')) {
      const nextPath = currentPath ? `${currentPath}/${segment}` : segment;
      const folderExists = this.getFolderByPath(nextPath) !== null;

      if (!folderExists) {
        const folderData: FolderData = {
          type: 'folder' as const,
          name: segment,
          totalElementCount: 0,
          loadedElementCount: 0,
          currentPage: 1,
          pageSize: 100
        }
        this.addChildrenNodeByPath(currentPath, folderData);
      }
      currentPath = nextPath;
    }
    this.addChildrenNodeByPath(currentPath, fileEntry);
  }

  canBeExpandedAsIsByKey(key: string) {
    const folder = this.getFolderByKey(key);
    if (!folder || folder.data?.type !== 'folder') {
      return false;
    }

    const loadedElementCount = folder.children?.length ?? 0;
    const totalElementCount = folder.data?.totalElementCount ?? 0;
    return loadedElementCount > 0 || totalElementCount === 0;
  }

  canBeExpandedAsIs(folderPath: string) {
    const folder = this.getFolderByPath(folderPath);
    if (!folder || folder.data?.type !== 'folder') {
      return false;
    }

    const loadedElementCount = folder.children?.length ?? 0;
    const totalElementCount = folder.data?.totalElementCount ?? 0;
    return loadedElementCount > 0 || totalElementCount === 0;
  }
  addChildrenNodeByPath(path: string | null, newChild: FileSystemEntry): boolean {
    return this.addChildrenNodes(this.getFolderByPath(path ?? '/'), [newChild]);
  }
  addChildrenNodes(node: TreeNode<FileSystemEntry> | null, newChildren: FileSystemEntry[]): boolean {
    if (node?.data?.type !== "folder") {
      return false;
    }
    const children = node.children ?? [];
    const lastNodeDataType: string = children[children.length - 1]?.data?.type ?? '';
    if (lastNodeDataType === 'loadMore') {
      children.pop();
    }

    const existingNames = new Set(children.map(child => child.data?.name));
    const uniqueNewChildren = newChildren
      // .filter(child => !existingNames.has(child.name))
      .map(c => {
        let node = createNode(c);
        if (this.autoExpandAll) {
          node.expanded = true;
        }
        return node;
      });
    node?.children?.push(...uniqueNewChildren);
    node.data.loadedElementCount += uniqueNewChildren.length;

    if (!areAllLoaded(node.data)) {
      node.children?.push(createLoadMoreNode());
      return false;
    }
    return true;
  }
  getByPath(path: string) {
    const pathSegments = getPathSegments(path);
    let currentNode = this.root;

    for (let segment of pathSegments) {
      const nextNode = currentNode.children?.find(n => n.data?.name === segment);
      if (!nextNode) {
        return null;
      }
      currentNode = nextNode;
    }
    return currentNode;
  }
  getByKey(key: string): TreeNode<FileSystemEntry> | null {
    return getByKeyRecursive(this.root, key);
  }
  getFolderByPath(path: string) {
    const node = this.getByPath(path);
    if (node && node.data && node.data.type === "folder") {
      return node as TreeNode<FolderData>;
    }
    return null;
  }
  getFolderByKey(key: string) {
    const node = this.getByKey(key);
    if (node && node.data && node.data.type === "folder") {
      return node as TreeNode<FolderData>;
    }
    return null;
  }
  updateFileByPath(filePath: string, updateFn: (file: FileData) => void) {
    const file = this.getByPath(filePath);
    if (file && file.data && file.data.type !== "folder") {
      updateFn(file.data);
    }
  }

  updateNodeByPath(filePath: string, updateFn: (node: TreeNode<FileSystemEntry>) => void) {
    const node = this.getByPath(filePath);
    if (node) {
      updateFn(node);
    }
  }

  getPathOf(key: string | null) {
    if (!key) {
      return null;
    }

    const segments = findPathSegmentsByKey(this.root, key);
    if (!segments) {
      return null;
    }

    return '/' + toPath(segments);
  }

  private flattenLeaves(nodes: TreeNode<FileSystemEntry>[]): TreeNode<FileSystemEntry>[] {
    return nodes.flatMap(n => n.leaf ? [n] : this.flattenLeaves(n.children ?? []));
  }
}

function getByKeyRecursive(node: TreeNode<FileSystemEntry>, key: string): TreeNode<FileSystemEntry> | null {
  // Check if key matches current node
  if (node.key === key) {
    return node;
  }

  // Recursively search children
  for (const child of node.children ?? []) {
    const found = getByKeyRecursive(child, key);
    if (found) {
      return found;
    }
  }
  return null;
}

function findPathSegmentsByKey(node: TreeNode<FileSystemEntry>, key: string, path: string[] = []): string[] | null {
  const newPath = [...path, node.data?.name ?? ''];

  if (node.key === key) {
    return newPath;
  }

  for (const child of node.children ?? []) {
    const childPath = findPathSegmentsByKey(child, key, newPath);
    if (childPath) {
      return childPath;
    }
  }

  return null;
}
