import {EventEmitter, Injectable} from '@angular/core';
import {FileSystemView} from './FileSystemView';
import {FileSystemViewMode, FileSystemViewProvider} from '../providers/file-system-view.provider';
import {WebApiClient} from '../client/client';
import {TreeNode} from 'primeng/api';
import {FileData, FileSystemEntry, FolderData} from './FileSystemEntry';
import {TreeNodeSelectEvent} from 'primeng/tree';
import {BehaviorSubject, Observable} from 'rxjs';
import {SearchCriteria} from '../app/file-search/search.criteria';
import {FileSystemViewId} from '../providers/file-system-view.id';

@Injectable({
  providedIn: 'root'
})
export class FileSystemState {
  private _stateId = new FileSystemViewId('status');
  selectedNodes: TreeNode<FileData>[] = [];

  readonly onSearchSuccess: EventEmitter<SearchCriteria> = new EventEmitter();
  readonly onSelectedFile: EventEmitter<string> = new EventEmitter();

  private lastSelectedNode: TreeNode<FileData> | null = null;
  private modelSubject = new BehaviorSubject<FileSystemView | null>(null);
  readonly model$: Observable<FileSystemView | null> = this.modelSubject.asObservable();

  constructor(
    private readonly fileSystemViewProvider: FileSystemViewProvider,
    private readonly client: WebApiClient,
  ) {
    this.updateModelFromServer();
  }

  get stateId() {
    return this._stateId;
  }
  set stateId(newId: FileSystemViewId) {
    if (this._stateId.equals(newId)) {
      return;
    }
    this._stateId = newId;
    this.updateModelLocally();
  }
  get isLoading() {
    return this.modelSubject.value === null;
  }
  get searchInProgress() {
    return this.searchCriteria !== null && this.modelSubject.value === null;
  }

  get viewMode() {
    return this.stateId.viewMode;
  }

  updateModelFromServer(): void {
    if (this.updateModelLocally()) {
      return;
    }

    if (this.searchCriteria !== null) {
      this.performSearch(this.searchCriteria);
      return;
    }
    this.loadRootMetadata();
  }
  private updateModelLocally(): boolean {
    const view = this.getView(this.stateId);
    if (view) {
      this.modelSubject.next(view);
      this.expandRoot();
      return true;
    }
    this.modelSubject.next(null);
    return false;
  }

  private loadRootMetadata() {
    const capturedStateId = this.stateId.clone();
    this.client.getFolderMetadata('/', capturedStateId.viewMode).subscribe({
      next: data => {
        const view = FileSystemView.fromFolderData(data as FolderData);
        this.fileSystemViewProvider.addViewIfDoesntExist(capturedStateId, view);
        this.updateModelFromServer();
      },
      error: error => {
        console.error('Error loading folder:', error);
      }
    });
  }
  changeViewMode(value: FileSystemViewMode) {
    if (this.isLoading) {
      return;
    }
    this.stateId = this.stateId.withViewMode(value);
    this.updateModelFromServer();
  }

  get searchCriteria() : SearchCriteria | null {
    return this.stateId.searchCriteria;
  }

  clearSearchCriteria() {
    if (this.stateId.searchCriteria === null) {
      return;
    }
    this.stateId = this.stateId.withSearchCriteria(null);
    this.updateModelLocally();
  }
  getView(id: FileSystemViewId) {
    return this.fileSystemViewProvider.getView(id);
  }

  expandRoot() {
    const model = this.modelSubject.value;
    if (!model) {
      console.error('Model is null');
      return;
    }

    if (model.canBeExpandedAsIs('/')) {
      model.root.expanded = true;
      return;
    }

    this.addChildrenFromServer(model.rootAsFolder, '/', true);
  }
  expand(folderPath: string | null = '/') {
    const model = this.modelSubject.value;
    if (folderPath == null) {
      console.error('Invalid folder path: ', folderPath);
      return;
    }
    if (!model) {
      console.error('Model is null');
      return;
    }
    const node = model.getByPath(folderPath)
    const isFolder = node?.data?.type === "folder";
    if (!isFolder) {
      console.error('Not a folder: ', folderPath);
      return;
    }

    if (model.canBeExpandedAsIs(folderPath)) {
      node.expanded = true;
      return;
    }

    this.addChildrenFromServer(node as TreeNode<FolderData>, folderPath, true);
  }

  loadNextPage(key: string): void {
    const capturedModel = this.modelSubject.value;
    if (!capturedModel) {
      return;
    }
    const node = capturedModel.getByKey(key);
    if (node?.data?.type !== 'folder') {
      return;
    }

    node.data.currentPage += 1;
    this.addChildrenFromServer(node as TreeNode<FolderData>, capturedModel.getPathOf(key) ?? '/', true);
  }

  loadAllFromServer(key: string): void {
    const capturedModel = this.modelSubject.value;
    if (!capturedModel) {
      return;
    }
    const node = capturedModel.getByKey(key);
    if (node?.data?.type !== 'folder') {
      return;
    }

    node.data.currentPage = 1;
    node.data.pageSize = node.data.totalElementCount;
    this.addChildrenFromServer(
      node as TreeNode<FolderData>,
      capturedModel.getPathOf(key) ?? '/',
      true,
      node.data.loadedElementCount,
    );
  }

  performSearch(criteria: SearchCriteria): void {
    this.stateId = this.stateId.withSearchCriteria(criteria);
    const capturedStateId = this.stateId.clone();
    const capturedSearchCriteria = criteria.clone();
    this.client.searchFiles(
      criteria.fromCreationDate,
      criteria.toCreationDate,
      criteria.status,
      criteria.caseId,
      this.viewMode,
    ).subscribe({
      next: response => {
        const view = FileSystemView.fromFilesWithPaths(response);
        this.fileSystemViewProvider.addViewIfDoesntExist(capturedStateId, view);
        this.onSearchSuccess.emit(capturedSearchCriteria);
        this.updateModelFromServer();
      },
      error: (error: any) => {
        console.error('Error performing search:', error);
      }
    });
  }

  onNodeSelect(event: TreeNodeSelectEvent): void {
    const { originalEvent, node } = event;
    const isShiftSelection =
      originalEvent instanceof MouseEvent &&
      originalEvent.shiftKey &&
      this.lastSelectedNode;
    const model = this.modelSubject.value;
    if (isShiftSelection && model) {
      const files = model.allFiles;
      const from = files.findIndex(n => n.key === this.lastSelectedNode!.key);
      const to = files.findIndex(n => n.key === node.key);

      if (from > -1 && to > -1) {
        const [lo, hi] = from < to ? [from, to] : [to, from];
        const range = files.slice(lo, hi + 1);
        this.selectedNodes = Array.from(new Set([...this.selectedNodes, ...range]));
        return;
      }
    }

    this.lastSelectedNode = node;

    if (node.data?.type === 'pdf' && model) {
      const filePath = model.getPathOf(node.key ?? null);
      if (filePath) {
        this.onSelectedFile.emit(filePath);
      }
    }
  }

  private addChildrenFromServer(node: TreeNode<FolderData>, folderPath: string, expand: boolean = false, offset: number = 0): void {
    const capturedView = this.getView(this.stateId);
    const capturedStateId = this.stateId.clone();

    if (!node.data || !capturedView) {
      console.error('Node data is missing or view is null: ', node);
      return;
    }
    if (node.loading) {
      return;
    }
    node.loading = true;
    this.client.listFolderChildren(
      folderPath,
      capturedStateId.viewMode,
      node.data.currentPage,
      node.data.pageSize,
      offset
    ).subscribe({
      next: data => {
        const currentView = this.getView(capturedStateId);
        if (!currentView) {
          return;
        }
        currentView.updateNodeByPath(folderPath, folder => {
          if (folder?.data?.type !== "folder") {
            return;
          }
          folder.data.currentPage = data.page;
          currentView.addChildrenNodes(folder, data.results as FileSystemEntry[]);

          folder.expanded = expand;
          folder.loading = false
        });
      },
      error: (error) => {
        const currentView = this.getView(capturedStateId);
        if (!currentView) {
          return;
        }
        console.error('Error loading folder:', error);
        currentView.updateNodeByPath(folderPath, node => node.loading = false);
      }
    });
  }
}
