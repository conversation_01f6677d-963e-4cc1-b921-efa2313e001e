import {TreeNode} from 'primeng/api';

export type FileSystemEntry = FileData | FolderData;


export interface FileData {
  readonly type: "pdf";
  readonly name: string;
  readonly size: number;
  isCrossed: boolean;
  isRed: boolean;
  hasNotification: boolean;
}

export interface FolderData {
  readonly type: "folder";
  readonly name: string;
  readonly totalElementCount: number;
  loadedElementCount: number;
  currentPage: number;
  pageSize: number;
}

export function areAllLoaded(data: FolderData) {
  return data.loadedElementCount >= data.totalElementCount;
}

type FolderNodeCreateData = Omit<FolderData, "type"> & {children: TreeNode<FileSystemEntry>[]}
export function createFolderNode(folderData: FolderNodeCreateData): TreeNode<FileSystemEntry> {
  return {
    key: crypto.randomUUID(),
    label: folderData.name === '' ? 'root' : folderData.name,
    expanded: false,
    leaf: false,
    selectable: false,
    type: "folder",
    data: {
      type: "folder",
      ...folderData
    },
    children: folderData.children,
  };
}

export function createFileNode(fileData: Omit<FileData, "type">): TreeNode<FileSystemEntry> {
  return {
    key: crypto.randomUUID(),
    label: fileData.name,
    type: "pdf",
    data: {
      type: "pdf",
      ...fileData
    },
    leaf: true
  };
}
export function createNode(nodeData: FileSystemEntry,
                           children: TreeNode<FileSystemEntry>[] = []):
  TreeNode<FileSystemEntry> {
  if (nodeData.type === "folder") {
    const folderData = nodeData as FolderData;
    return createFolderNode({
      name: folderData.name,
      currentPage: 1,
      loadedElementCount: children.length,
      totalElementCount: folderData.totalElementCount,
      children: children,
      pageSize: 10
    });
  }
  return createFileNode(nodeData)
}

export function createLoadMoreNode() {
  return {
    key: crypto.randomUUID(),
    label: "Load more",
    leaf: true,
    selectable: false,
    type: "loadMore",
    data: {
      type: "loadMore",
      name: "Load more",
    }
  } as TreeNode;
}
