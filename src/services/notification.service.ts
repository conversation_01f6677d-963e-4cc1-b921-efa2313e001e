// notification.service.ts
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';

export interface NotificationMessage {
  type: string;
  message: string;
  path: string;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private socket: WebSocket | null = null;
  private notificationSubject = new Subject<NotificationMessage>();
  private connectionStatusSubject = new Subject<boolean>();

  public notifications$ = this.notificationSubject.asObservable();
  public connectionStatus$ = this.connectionStatusSubject.asObservable();

  constructor() {
    // Automatically connect when the service is initialized
    this.connect();
  }

  /**
   * Connect to the WebSocket notification service
   */
  connect(): void {
    // Close any existing connection
    this.disconnect();

    // Create a new WebSocket connection
    this.socket = new WebSocket('ws://localhost:8000/ws');

    // Connection opened
    this.socket.addEventListener('open', () => {
      console.log('WebSocket connection established');
      this.connectionStatusSubject.next(true);
      this.socket?.send("{ \"type\": \"message\", \"path\": \"/\" }");
    });

    // Listen for messages
    this.socket.addEventListener('message', (event) => {
      try {
        const data = JSON.parse(event.data) as NotificationMessage;
        console.log('Notification received:', data);
        this.notificationSubject.next(data);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    });

    // Connection closed
    this.socket.addEventListener('close', () => {
      console.log('WebSocket connection closed');
      this.connectionStatusSubject.next(false);

      // Optional: Attempt to reconnect after a delay
      setTimeout(() => this.connect(), 5000);
    });

    // Connection error
    this.socket.addEventListener('error', (error) => {
      console.error('WebSocket error:', error);
      this.connectionStatusSubject.next(false);
    });

  }

  /**
   * Disconnect from the WebSocket notification service
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
      this.connectionStatusSubject.next(false);
    }
  }
}
