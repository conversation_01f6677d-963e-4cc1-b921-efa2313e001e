//----------------------
// <auto-generated>
//     Generated using the NSwag toolchain v14.3.0.0 (NJsonSchema v11.2.0.0 (Newtonsoft.Json v13.0.0.0)) (http://NSwag.org)
// </auto-generated>
//----------------------

/* tslint:disable */
/* eslint-disable */
// ReSharper disable InconsistentNaming

import { mergeMap as _observableMergeMap, catchError as _observableCatch } from 'rxjs/operators';
import { Observable, throwError as _observableThrow, of as _observableOf } from 'rxjs';
import { Injectable, Inject, Optional, InjectionToken } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse, HttpResponseBase } from '@angular/common/http';

export const API_BASE_URL = new InjectionToken<string>('API_BASE_URL');

@Injectable({
    providedIn: 'root'
})
export class WebApiClient {
    private http: HttpClient;
    private baseUrl: string;
    protected jsonParseReviver: ((key: string, value: any) => any) | undefined = undefined;

    constructor(@Inject(HttpClient) http: HttpClient, @Optional() @Inject(API_BASE_URL) baseUrl?: string) {
        this.http = http;
        this.baseUrl = baseUrl ?? "";
    }

    /**
     * Login and get access token
     * @return Successfully authenticated
     */
    loginForAccessToken(body: Body_loginForAccessToken): Observable<Token> {
        let url_ = this.baseUrl + "/api/token";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = Object.keys(body as any).map((key) => {
            return encodeURIComponent(key) + '=' + encodeURIComponent((body as any)[key]);
        }).join('&')

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processLoginForAccessToken(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processLoginForAccessToken(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<Token>;
                }
            } else
                return _observableThrow(response_) as any as Observable<Token>;
        }));
    }

    protected processLoginForAccessToken(response: HttpResponseBase): Observable<Token> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            result200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as Token;
            return _observableOf(result200);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("Authentication failed", status, _responseText, _headers);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result422: any = null;
            result422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as HTTPValidationError;
            return throwException("Validation Error", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Register a new user
     * @return User successfully created
     */
    createUser(body: UserCreate): Observable<any> {
        let url_ = this.baseUrl + "/api/users";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processCreateUser(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processCreateUser(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<any>;
                }
            } else
                return _observableThrow(response_) as any as Observable<any>;
        }));
    }

    protected processCreateUser(response: HttpResponseBase): Observable<any> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 201) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result201: any = null;
            result201 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as any;
            return _observableOf(result201);
            }));
        } else if (status === 400) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("Registration failed", status, _responseText, _headers);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result422: any = null;
            result422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as HTTPValidationError;
            return throwException("Validation Error", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Get current user information
     * @return Current user information
     */
    getCurrentUser(): Observable<any> {
        let url_ = this.baseUrl + "/api/users/me";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetCurrentUser(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetCurrentUser(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<any>;
                }
            } else
                return _observableThrow(response_) as any as Observable<any>;
        }));
    }

    protected processGetCurrentUser(response: HttpResponseBase): Observable<any> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            result200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as any;
            return _observableOf(result200);
            }));
        } else if (status === 401) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("Not authenticated", status, _responseText, _headers);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Get folder metadata
     * @param path (optional) 
     * @param viewMode (optional) 
     * @return Successful Response
     */
    getFolderMetadata(path: string | undefined, viewMode: string | undefined): Observable<FolderData> {
        let url_ = this.baseUrl + "/api/meta?";
        if (path === null)
            throw new Error("The parameter 'path' cannot be null.");
        else if (path !== undefined)
            url_ += "path=" + encodeURIComponent("" + path) + "&";
        if (viewMode === null)
            throw new Error("The parameter 'viewMode' cannot be null.");
        else if (viewMode !== undefined)
            url_ += "viewMode=" + encodeURIComponent("" + viewMode) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processGetFolderMetadata(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processGetFolderMetadata(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FolderData>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FolderData>;
        }));
    }

    protected processGetFolderMetadata(response: HttpResponseBase): Observable<FolderData> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            result200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as FolderData;
            return _observableOf(result200);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result422: any = null;
            result422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as HTTPValidationError;
            return throwException("Validation Error", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * List immediate children of a folder
     * @param path (optional) 
     * @param viewMode (optional) 
     * @param page (optional) 
     * @param page_size (optional) 
     * @param offset (optional) 
     * @return Successful Response
     */
    listFolderChildren(path: string | undefined, viewMode: string | undefined, page: number | undefined, page_size: number | undefined, offset: number | undefined): Observable<FolderItemsResponse> {
        let url_ = this.baseUrl + "/api/children?";
        if (path === null)
            throw new Error("The parameter 'path' cannot be null.");
        else if (path !== undefined)
            url_ += "path=" + encodeURIComponent("" + path) + "&";
        if (viewMode === null)
            throw new Error("The parameter 'viewMode' cannot be null.");
        else if (viewMode !== undefined)
            url_ += "viewMode=" + encodeURIComponent("" + viewMode) + "&";
        if (page === null)
            throw new Error("The parameter 'page' cannot be null.");
        else if (page !== undefined)
            url_ += "page=" + encodeURIComponent("" + page) + "&";
        if (page_size === null)
            throw new Error("The parameter 'page_size' cannot be null.");
        else if (page_size !== undefined)
            url_ += "page_size=" + encodeURIComponent("" + page_size) + "&";
        if (offset === null)
            throw new Error("The parameter 'offset' cannot be null.");
        else if (offset !== undefined)
            url_ += "offset=" + encodeURIComponent("" + offset) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processListFolderChildren(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processListFolderChildren(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FolderItemsResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FolderItemsResponse>;
        }));
    }

    protected processListFolderChildren(response: HttpResponseBase): Observable<FolderItemsResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            result200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as FolderItemsResponse;
            return _observableOf(result200);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result422: any = null;
            result422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as HTTPValidationError;
            return throwException("Validation Error", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Search files
     * @param fromCreationDate (optional) Start date for file creation
     * @param toCreationDate (optional) End date for file creation
     * @param status (optional) Status of the file
     * @param caseId (optional) Case ID associated with the file
     * @param viewMode (optional) View mode to organize results
     * @return Successful Response
     */
    searchFiles(fromCreationDate: Date | undefined, toCreationDate: Date | undefined, status: string | undefined, caseId: string | undefined, viewMode: string | undefined): Observable<FileDataWithPath[]> {
        let url_ = this.baseUrl + "/api/files?";
        if (fromCreationDate === null)
            throw new Error("The parameter 'fromCreationDate' cannot be null.");
        else if (fromCreationDate !== undefined)
            url_ += "fromCreationDate=" + encodeURIComponent(fromCreationDate ? "" + fromCreationDate.toISOString() : "") + "&";
        if (toCreationDate === null)
            throw new Error("The parameter 'toCreationDate' cannot be null.");
        else if (toCreationDate !== undefined)
            url_ += "toCreationDate=" + encodeURIComponent(toCreationDate ? "" + toCreationDate.toISOString() : "") + "&";
        if (status === null)
            throw new Error("The parameter 'status' cannot be null.");
        else if (status !== undefined)
            url_ += "status=" + encodeURIComponent("" + status) + "&";
        if (caseId === null)
            throw new Error("The parameter 'caseId' cannot be null.");
        else if (caseId !== undefined)
            url_ += "caseId=" + encodeURIComponent("" + caseId) + "&";
        if (viewMode === null)
            throw new Error("The parameter 'viewMode' cannot be null.");
        else if (viewMode !== undefined)
            url_ += "viewMode=" + encodeURIComponent("" + viewMode) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/json"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processSearchFiles(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processSearchFiles(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FileDataWithPath[]>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FileDataWithPath[]>;
        }));
    }

    protected processSearchFiles(response: HttpResponseBase): Observable<FileDataWithPath[]> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            result200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as FileDataWithPath[];
            return _observableOf(result200);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result422: any = null;
            result422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as HTTPValidationError;
            return throwException("Validation Error", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Download a file
     * @param path (optional) 
     * @param viewMode (optional) 
     * @return PDF file
     */
    downloadFile(path: string | undefined, viewMode: string | undefined): Observable<FileResponse> {
        let url_ = this.baseUrl + "/api/download?";
        if (path === null)
            throw new Error("The parameter 'path' cannot be null.");
        else if (path !== undefined)
            url_ += "path=" + encodeURIComponent("" + path) + "&";
        if (viewMode === null)
            throw new Error("The parameter 'viewMode' cannot be null.");
        else if (viewMode !== undefined)
            url_ += "viewMode=" + encodeURIComponent("" + viewMode) + "&";
        url_ = url_.replace(/[?&]$/, "");

        let options_ : any = {
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Accept": "application/pdf"
            })
        };

        return this.http.request("get", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processDownloadFile(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processDownloadFile(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FileResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FileResponse>;
        }));
    }

    protected processDownloadFile(response: HttpResponseBase): Observable<FileResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200 || status === 206) {
            const contentDisposition = response.headers ? response.headers.get("content-disposition") : undefined;
            let fileNameMatch = contentDisposition ? /filename\*=(?:(\\?['"])(.*?)\1|(?:[^\s]+'.*?')?([^;\n]*))/g.exec(contentDisposition) : undefined;
            let fileName = fileNameMatch && fileNameMatch.length > 1 ? fileNameMatch[3] || fileNameMatch[2] : undefined;
            if (fileName) {
                fileName = decodeURIComponent(fileName);
            } else {
                fileNameMatch = contentDisposition ? /filename="?([^"]*?)"?(;|$)/g.exec(contentDisposition) : undefined;
                fileName = fileNameMatch && fileNameMatch.length > 1 ? fileNameMatch[1] : undefined;
            }
            return _observableOf({ fileName: fileName, data: responseBlob as any, status: status, headers: _headers });
        } else if (status === 404) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result404: any = null;
            result404 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as ErrorResponse;
            return throwException("File not found", status, _responseText, _headers, result404);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result422: any = null;
            result422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as HTTPValidationError;
            return throwException("Validation Error", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Filter file paths (Action 1)
     * @return Successful Response
     */
    performAction1(body: FilePathsRequest): Observable<FilePathsResponse> {
        let url_ = this.baseUrl + "/api/action1";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processPerformAction1(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processPerformAction1(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FilePathsResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FilePathsResponse>;
        }));
    }

    protected processPerformAction1(response: HttpResponseBase): Observable<FilePathsResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            result200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as FilePathsResponse;
            return _observableOf(result200);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result422: any = null;
            result422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as HTTPValidationError;
            return throwException("Validation Error", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }

    /**
     * Filter file paths (Action 2)
     * @return Successful Response
     */
    performAction2(body: FilePathsRequest): Observable<FilePathsResponse> {
        let url_ = this.baseUrl + "/api/action2";
        url_ = url_.replace(/[?&]$/, "");

        const content_ = JSON.stringify(body);

        let options_ : any = {
            body: content_,
            observe: "response",
            responseType: "blob",
            headers: new HttpHeaders({
                "Content-Type": "application/json",
                "Accept": "application/json"
            })
        };

        return this.http.request("post", url_, options_).pipe(_observableMergeMap((response_ : any) => {
            return this.processPerformAction2(response_);
        })).pipe(_observableCatch((response_: any) => {
            if (response_ instanceof HttpResponseBase) {
                try {
                    return this.processPerformAction2(response_ as any);
                } catch (e) {
                    return _observableThrow(e) as any as Observable<FilePathsResponse>;
                }
            } else
                return _observableThrow(response_) as any as Observable<FilePathsResponse>;
        }));
    }

    protected processPerformAction2(response: HttpResponseBase): Observable<FilePathsResponse> {
        const status = response.status;
        const responseBlob =
            response instanceof HttpResponse ? response.body :
            (response as any).error instanceof Blob ? (response as any).error : undefined;

        let _headers: any = {}; if (response.headers) { for (let key of response.headers.keys()) { _headers[key] = response.headers.get(key); }}
        if (status === 200) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result200: any = null;
            result200 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as FilePathsResponse;
            return _observableOf(result200);
            }));
        } else if (status === 422) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            let result422: any = null;
            result422 = _responseText === "" ? null : JSON.parse(_responseText, this.jsonParseReviver) as HTTPValidationError;
            return throwException("Validation Error", status, _responseText, _headers, result422);
            }));
        } else if (status !== 200 && status !== 204) {
            return blobToText(responseBlob).pipe(_observableMergeMap((_responseText: string) => {
            return throwException("An unexpected server error occurred.", status, _responseText, _headers);
            }));
        }
        return _observableOf(null as any);
    }
}

export interface Body_loginForAccessToken {
    grant_type?: Grant_type;
    username: string;
    password: string;
    scope?: string;
    client_id?: Client_id;
    client_secret?: Client_secret;

    [key: string]: any;
}

export interface ErrorResponse {
    detail: string;

    [key: string]: any;
}

export interface FileData {
    name: string;
    size: number;
    type?: string;

    [key: string]: any;
}

export interface FileDataWithPath {
    /** Folder path (e.g. `/folder1/folder2`). Root is `/` or omitted. */
    path?: string;
    /** View mode to organize results */
    viewMode?: string;
    size: number;
    type?: string;

    [key: string]: any;
}

export interface FilePathsRequest {
    filePaths: string[];

    [key: string]: any;
}

export interface FilePathsResponse {
    filePaths: string[];

    [key: string]: any;
}

export interface FolderData {
    name: string;
    totalElementCount: number;
    type?: string;

    [key: string]: any;
}

export interface FolderItemsResponse {
    count: number;
    page: number;
    page_size: number;
    offset: number;
    results: FileData[];

    [key: string]: any;
}

export interface HTTPValidationError {
    detail?: ValidationError[];

    [key: string]: any;
}

/** JWT token response model */
export interface Token {
    /** JWT access token */
    access_token: string;
    /** Token type (bearer) */
    token_type: string;

    [key: string]: any;
}

/** User creation model with password */
export interface UserCreate {
    /** Unique username */
    username: string;
    /** User's email address */
    email: string;
    /** Whether the user account is active */
    is_active?: boolean;
    /** User's password (will be hashed) */
    password: string;

    [key: string]: any;
}

export interface ValidationError {
    loc: Loc[];
    msg: string;
    type: string;

    [key: string]: any;
}

export interface Grant_type {

    [key: string]: any;
}

export interface Client_id {

    [key: string]: any;
}

export interface Client_secret {

    [key: string]: any;
}

export interface Loc {

    [key: string]: any;
}

export interface FileResponse {
    data: Blob;
    status: number;
    fileName?: string;
    headers?: { [name: string]: any };
}

export class ApiException extends Error {
    override message: string;
    status: number;
    response: string;
    headers: { [key: string]: any; };
    result: any;

    constructor(message: string, status: number, response: string, headers: { [key: string]: any; }, result: any) {
        super();

        this.message = message;
        this.status = status;
        this.response = response;
        this.headers = headers;
        this.result = result;
    }

    protected isApiException = true;

    static isApiException(obj: any): obj is ApiException {
        return obj.isApiException === true;
    }
}

function throwException(message: string, status: number, response: string, headers: { [key: string]: any; }, result?: any): Observable<any> {
    if (result !== null && result !== undefined)
        return _observableThrow(result);
    else
        return _observableThrow(new ApiException(message, status, response, headers, null));
}

function blobToText(blob: any): Observable<string> {
    return new Observable<string>((observer: any) => {
        if (!blob) {
            observer.next("");
            observer.complete();
        } else {
            let reader = new FileReader();
            reader.onload = event => {
                observer.next((event.target as any).result);
                observer.complete();
            };
            reader.readAsText(blob);
        }
    });
}