// Lightweight, type‑safe wrapper for the three authentication endpoints
// defined in the FastAPI OpenAPI schema.

import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { firstValueFrom } from 'rxjs';
import { API_BASE_URL } from './client';

export interface TokenResponse {
  /** JWT access token */
  access_token: string;
  /** Token type (always "bearer" for this API) */
  token_type: string;
}

export interface LoginRequest {
  username: string;
  password: string;
  /** Optional OAuth2 scope string (defaults to empty) */
  scope?: string;
}

export interface UserCreateRequest {
  /** Unique username */
  username: string;
  /** User email address */
  email: string;
  /** Plain‑text password; will be hashed server‑side */
  password: string;
}

export interface UserResponse {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
}

export interface ErrorResponse {
  detail: string;
}

/**
 * AuthClient – minimal client for the FastAPI auth endpoints using Angular HttpClient.
 *
 * All methods return typed payloads and throw an Error (with the server‑provided
 * `detail` if available) when the HTTP status is not 2xx.
 */
@Injectable({
  providedIn: "root"
})
export class AuthClient {
  private token?: string;

  constructor(
    @Inject(API_BASE_URL) private readonly baseUrl: string,
    private readonly http: HttpClient
  ) {}

  /** Replace or clear the bearer token used by `getCurrentUser`. */
  setToken(token?: string) {
    this.token = token;
  }

  /** Helper to build absolute URLs without worrying about slashes. */
  private buildUrl(path: string): string {
    return `${this.baseUrl.replace(/\/+$/, "")}${path}`;
  }

  /**
   * POST /api/token – Exchange username/password for a JWT access token.
   */
  async login({ username, password, scope = "" }: LoginRequest): Promise<TokenResponse> {
    const body = new URLSearchParams({ grant_type: "password", username, password, scope }).toString();

    const headers = new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });

    try {
      const tokenResponse = await firstValueFrom(
        this.http.post<TokenResponse>(this.buildUrl("/api/token"), body, { headers })
      );

      // Optionally store the token for subsequent requests
      this.token = tokenResponse.access_token;
      return tokenResponse;
    } catch (error: any) {
      const message = error?.error?.detail ?? error?.message ?? 'Login failed';
      throw new Error(message);
    }
  }

  /**
   * POST /api/users – Register a new user account.
   */
  async register(request: UserCreateRequest): Promise<UserResponse> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    try {
      return await firstValueFrom(
        this.http.post<UserResponse>(this.buildUrl("/api/users"), request, { headers })
      );
    } catch (error: any) {
      const message = error?.error?.detail ?? error?.message ?? 'Registration failed';
      throw new Error(message);
    }
  }

  /**
   * GET /api/users/me – Fetch the profile of the currently authenticated user.
   *
   * If a token is supplied as an argument it overrides the internally‑stored one.
   */
  async getCurrentUser(tokenOverride?: string): Promise<UserResponse> {
    const token = tokenOverride ?? this.token;
    if (!token) {
      throw new Error("getCurrentUser requires a bearer token – call login() first or supply one explicitly");
    }

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    try {
      return await firstValueFrom(
        this.http.get<UserResponse>(this.buildUrl("/api/users/me"), { headers })
      );
    } catch (error: any) {
      const message = error?.error?.detail ?? error?.message ?? 'Failed to get user info';
      throw new Error(message);
    }
  }
}
