import {EventEmitter, Injectable} from '@angular/core';
import {DocumentId} from './document-id';

export interface Document {
  localPath: string;
}

@Injectable({
  providedIn: 'root'
})
export class DocumentProvider {
  private readonly documents: Map<string, Document> = new Map();
  onDocumentAdded = new EventEmitter<DocumentId>();
  onDocumentRemoved = new EventEmitter<DocumentId>();
  onDocumentUpdated = new EventEmitter<DocumentId>();

  constructor() {}

  addDocumentIfDoesntExist(id: DocumentId, document: Document): Document {
    const key = id.toString();
    const existingDocument = this.documents.get(key);
    if (existingDocument) {
      return existingDocument;
    }
    this.documents.set(key, document);
    this.onDocumentAdded.emit(id);
    return document;
  }

  getDocument(id: DocumentId): Document | null {
    return this.documents.get(id.toString()) || null;
  }

  updateDocument(id: DocumentId, document: Document): void {
    const key = id.toString();
    this.documents.set(key, document);
    this.onDocumentUpdated.emit(id);
  }

  deleteDocument(id: DocumentId): void {
    this.documents.delete(id.toString());
    this.onDocumentRemoved.emit(id);
  }

  hasDocument(id: DocumentId): boolean {
    return this.documents.has(id.toString());
  }

  getAllDocumentIds(): DocumentId[] {
    return Array.from(this.documents.keys())
      .map(key => DocumentId.fromString(key));
  }
}

