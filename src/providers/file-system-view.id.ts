import {SearchCriteria} from "../app/file-search/search.criteria";
import {FileSystemViewMode} from "./file-system-view.provider";

export class FileSystemViewId {
  constructor(
    public readonly viewMode: FileSystemViewMode,
    public readonly searchCriteria: SearchCriteria | null = null
  ) {}

  equals(other: FileSystemViewId): boolean {
    if (this.viewMode !== other.viewMode) return false;

    // If both have null searchCriteria, they're equal
    if (this.searchCriteria === other.searchCriteria) return true;

    // If only one has null searchCriteria, they're not equal
    if (!this.searchCriteria || !other.searchCriteria) return false;

    // Use the equals method from SearchCriteria
    return this.searchCriteria.equals(other.searchCriteria);
  }

  withSearchCriteria(searchCriteria: SearchCriteria | null): FileSystemViewId {
    return new FileSystemViewId(this.viewMode, searchCriteria);
  }

  withViewMode(viewMode: FileSystemViewMode): FileSystemViewId {
    return new FileSystemViewId(viewMode, this.searchCriteria);
  }

  clone(): FileSystemViewId {
    return new FileSystemViewId(
      this.viewMode,
      this.searchCriteria ? this.searchCriteria.clone() : null
    );
  }

  toString(): string {
    return JSON.stringify({
      viewMode: this.viewMode,
      searchCriteria: this.searchCriteria
    });
  }

  static fromString(str: string): FileSystemViewId {
    const obj = JSON.parse(str);
    return new FileSystemViewId(
      obj.viewMode,
      obj.searchCriteria ? SearchCriteria.from(obj.searchCriteria) : null
    );
  }

  formatted() {
    return `${this.viewMode} - ${this.searchCriteria?.formatted()}`;
  }

  get isSearchView() {
    return this.searchCriteria !== null;
  }
}
