import {EventEmitter, Injectable} from '@angular/core';
import {FileSystemView} from '../state/FileSystemView';
import {FileSystemViewId} from './file-system-view.id';

@Injectable({
  providedIn: 'root'
})
export class FileSystemViewProvider {
  private readonly normalViews: Map<FileSystemViewMode, FileSystemView> = new Map();
  private readonly _searchViews: Map<string, FileSystemView> = new Map();
  onViewAdded = new EventEmitter<FileSystemViewId>();
  onViewRemoved = new EventEmitter<FileSystemViewId>();

  constructor() { }

  addViewIfDoesntExist(id: FileSystemViewId, view: FileSystemView) {
    const searchCriteria = id.searchCriteria;
    if (searchCriteria) {
      const key = id.toString();
      const existingView = this._searchViews.get(key);
      if (existingView) {
        return existingView;
      }
      this._searchViews.set(key, view);
      this.onViewAdded.emit(id);
      return view;
    }
    const viewMode = id.viewMode;
    const existingView = this.normalViews.get(viewMode);
    if (existingView) {
      return existingView;
    }
    this.normalViews.set(viewMode, view);
    this.onViewAdded.emit(id);
    return view;
  }

  getView(id: FileSystemViewId): FileSystemView | null {
    if (id.searchCriteria) {
      return this._searchViews.get(id.toString()) || null;
    }
    return this.normalViews.get(id.viewMode) || null;
  }

  deleteView(id: FileSystemViewId) {
    this._searchViews.delete(id.toString());
    this.onViewRemoved.emit(id);
  }
}

export type FileSystemViewMode = 'status' | 'date' | 'case';
