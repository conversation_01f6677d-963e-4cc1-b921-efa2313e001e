import {FileSystemViewMode} from "./file-system-view.provider";

export class DocumentId {
  constructor(
    public readonly path: string,
    public readonly mode: FileSystemViewMode,
  ) { }

  equals(other: DocumentId): boolean {
    return this.path === other.path && this.mode === other.mode;
  }

  clone(): DocumentId {
    return new DocumentId(this.path, this.mode);
  }

  toString(): string {
    return JSON.stringify({
      path: this.path,
      mode: this.mode
    });
  }

  static fromString(str: string): DocumentId {
    const obj = JSON.parse(str);
    return new DocumentId(obj.path, obj.mode);
  }

  withPath(path: string): DocumentId {
    return new DocumentId(path, this.mode);
  }

  withMode(mode: FileSystemViewMode): DocumentId {
    return new DocumentId(this.path, mode);
  }

  formatted(): string {
    return `${this.path} (${this.mode})`;
  }
}
