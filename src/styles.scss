@tailwind base;
@tailwind components;
@tailwind utilities;

@import "primeicons/primeicons.css";

// Add any global styles below this line

// Prevent body scrolling when mobile menu is open
body.mobile-menu-open {
  overflow: hidden;
}

// Global PrimeNG v19 password component fixes
.p-password {
  width: 100% !important;
  display: block !important;
  flex: 1 !important;

  .p-password-input {
    width: 100% !important;
    box-sizing: border-box !important;
    flex: 1 !important;
  }

  .p-inputtext {
    width: 100% !important;
    box-sizing: border-box !important;
    flex: 1 !important;
  }
}

// Specific fix for password components in flex containers
.input-wrapper .p-password,
.p-fluid .p-password {
  width: 100% !important;
  flex: 1 !important;
}
