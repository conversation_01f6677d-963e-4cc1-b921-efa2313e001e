import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { SafeResourceUrlPipe } from '../pipes/safe-resource-url.pipe';
import { WebApiClient } from '../../client/client';

@Component({
  selector: 'app-pdf-viewer',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    MenuModule,
    SafeResourceUrlPipe
  ],
  templateUrl: './pdf-viewer.component.html',
  styleUrl: './pdf-viewer.component.scss'
})
export class PdfViewerComponent {
  @Input() filePath: string | null = null;
  @Output() close = new EventEmitter<void>();

  selectedFile: { path: string, url: string | null, isCrossed?: boolean, isRed?: boolean } | null = null;
  isLoadingPdf = false;
  activeTab: 'document' | 'events' | 'transactions' | 'emails' = 'document';

  constructor(private readonly client: WebApiClient) {}

  ngOnChanges(): void {
    if (this.filePath) {
      this.displayPdfFile(this.filePath);
    } else {
      this.selectedFile = null;
    }
  }

  displayPdfFile(filePath: string): void {
    this.isLoadingPdf = true;
    this.selectedFile = { path: filePath, url: null };
    this.activeTab = 'document';

    this.client.downloadFile(filePath, 'inline').subscribe({
      next: (file) => {
        const url = URL.createObjectURL(file.data);
        this.selectedFile = { path: filePath, url };
        this.isLoadingPdf = false;
      },
      error: (error) => {
        console.error('Error downloading file:', error);
        this.isLoadingPdf = false;
      }
    });
  }

  refreshPdf(): void {
    if (this.selectedFile) {
      // Revoke the old URL to prevent memory leaks
      if (this.selectedFile.url) {
        URL.revokeObjectURL(this.selectedFile.url);
      }

      // Reload the PDF
      this.displayPdfFile(this.selectedFile.path);
    }
  }

  closeViewer(): void {
    // Clean up any resources
    if (this.selectedFile?.url) {
      URL.revokeObjectURL(this.selectedFile.url);
    }
    this.selectedFile = null;
    this.close.emit();
  }


}
