<div *ngIf="selectedFile !== null" class="pdf-viewer-container shadow-xl rounded-2xl ring-1 ring-blue-300 flex flex-col">
  <div class="pdf-viewer-header flex items-center justify-between p-2 border-b border-blue-300 bg-gradient-to-r from-blue-100 to-blue-50 rounded-t-2xl">
    <div class="flex items-center">
      <i class="pi pi-file-pdf text-red-600 text-xl mr-2"></i>
      <h2 class="text-lg font-medium text-blue-700"
          [ngClass]="{'crossed-text': selectedFile.isCrossed, 'red-text': selectedFile.isRed}"
          title="{{ selectedFile.path.split('/').pop() }}">{{ selectedFile.path.split('/').pop() }}</h2>

      <!-- Document status indicators -->
      <div class="document-status" *ngIf="selectedFile.isCrossed || selectedFile.isRed">
        <div class="status-indicator status-marked" *ngIf="selectedFile.isCrossed" title="Document is marked"></div>
        <div class="status-indicator status-flagged" *ngIf="selectedFile.isRed" title="Document is flagged"></div>
      </div>
    </div>
    <div class="flex items-center gap-2">
      <button class="p-button p-button-rounded p-button-outlined p-button-primary refresh-btn" (click)="refreshPdf()" title="Refresh PDF">
        <i class="pi pi-sync"></i>
      </button>

      <button class="p-button p-button-rounded p-button-danger close-btn" (click)="closeViewer()" title="Close Viewer">
        <i class="pi pi-times"></i>
      </button>
    </div>
  </div>

  <!-- Tabs -->
  <div class="pdf-viewer-tabs border-b border-blue-300">
    <div class="tabs-container">
      <button
        class="tab-button"
        [ngClass]="{'active': activeTab === 'document'}"
        (click)="activeTab = 'document'">
        <i class="pi pi-file-pdf mr-1"></i>
        Document
      </button>
      <button
        class="tab-button"
        [ngClass]="{'active': activeTab === 'events'}"
        (click)="activeTab = 'events'">
        <i class="pi pi-calendar mr-1"></i>
        Events
      </button>
      <button
        class="tab-button"
        [ngClass]="{'active': activeTab === 'transactions'}"
        (click)="activeTab = 'transactions'">
        <i class="pi pi-dollar mr-1"></i>
        Transactions
      </button>
      <button
        class="tab-button"
        [ngClass]="{'active': activeTab === 'emails'}"
        (click)="activeTab = 'emails'">
        <i class="pi pi-envelope mr-1"></i>
        Emails
      </button>
    </div>
  </div>

  <!-- Tab Content -->
  <div class="pdf-viewer-content">
    <!-- Document Tab (PDF) -->
    <div class="tab-content pdf-document-content" [class.active]="activeTab === 'document'">
      <!-- Loading overlay -->
      <div *ngIf="isLoadingPdf" class="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
        <div class="loading-spinner-large"></div>
        <h3 class="loading-text ml-3">Loading PDF...</h3>
      </div>

      <!-- PDF iframe with scrollable container -->
      <div class="pdf-scroll-container">
        <iframe *ngIf="selectedFile?.url"
                [src]="selectedFile!.url | safeResourceUrl"
                class="w-full h-full border-none"
                title="PDF Viewer">
        </iframe>
      </div>
    </div>

    <!-- Events Tab -->
    <div class="tab-content tab-scrollable-content" [class.active]="activeTab === 'events'">
      <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
        <h3 class="text-lg font-medium text-blue-700 mb-2">Document Events</h3>
        <div class="event-list">
          <div class="event-item">
            <div class="event-icon"><i class="pi pi-check-circle text-green-500"></i></div>
            <div class="event-details">
              <div class="event-title">Document Created</div>
              <div class="event-time">Today, 10:30 AM</div>
              <div class="event-description">Document was uploaded to the system</div>
            </div>
          </div>
          <div class="event-item">
            <div class="event-icon"><i class="pi pi-eye text-blue-500"></i></div>
            <div class="event-details">
              <div class="event-title">Document Viewed</div>
              <div class="event-time">Today, 11:15 AM</div>
              <div class="event-description">Document was viewed by John Doe</div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <h3 class="text-lg font-medium text-blue-700 mb-2">Flag Events</h3>
        <div class="event-list">
          <div class="event-item">
            <div class="event-icon"><i class="pi pi-flag-fill text-red-500"></i></div>
            <div class="event-details">
              <div class="event-title">Document Flagged</div>
              <div class="event-time">Today, 12:45 PM</div>
              <div class="event-description">Document was flagged for review</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Transactions Tab -->
    <div class="tab-content tab-scrollable-content" [class.active]="activeTab === 'transactions'">
      <div class="bg-white rounded-lg shadow-sm p-4">
        <h3 class="text-lg font-medium text-blue-700 mb-4">Transaction History</h3>
        <div class="transaction-list">
          <div class="transaction-item">
            <div class="transaction-icon"><i class="pi pi-dollar text-green-500"></i></div>
            <div class="transaction-details">
              <div class="transaction-title">Payment Received</div>
              <div class="transaction-amount">$1,250.00</div>
              <div class="transaction-time">June 15, 2023</div>
              <div class="transaction-status">Completed</div>
            </div>
          </div>
          <div class="transaction-item">
            <div class="transaction-icon"><i class="pi pi-dollar text-blue-500"></i></div>
            <div class="transaction-details">
              <div class="transaction-title">Invoice Generated</div>
              <div class="transaction-amount">$750.00</div>
              <div class="transaction-time">June 10, 2023</div>
              <div class="transaction-status">Pending</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Emails Tab -->
    <div class="tab-content tab-scrollable-content" [class.active]="activeTab === 'emails'">
      <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
        <h3 class="text-lg font-medium text-blue-700 mb-2">Email Correspondence</h3>
        <div class="email-list">
          <div class="email-item">
            <div class="email-icon"><i class="pi pi-envelope text-blue-500"></i></div>
            <div class="email-details">
              <div class="email-subject">Document Review Request</div>
              <div class="email-sender">From: john.doe&#64;example.com</div>
              <div class="email-time">June 15, 2023, 2:30 PM</div>
              <div class="email-preview">Please review the attached document and provide feedback...</div>
            </div>
          </div>
          <div class="email-item">
            <div class="email-icon"><i class="pi pi-envelope text-blue-500"></i></div>
            <div class="email-details">
              <div class="email-subject">Updated Document Version</div>
              <div class="email-sender">From: jane.smith&#64;example.com</div>
              <div class="email-time">June 12, 2023, 10:15 AM</div>
              <div class="email-preview">I've attached the updated version of the document with the requested changes...</div>
            </div>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <h3 class="text-lg font-medium text-blue-700 mb-2">Compose Email</h3>
        <div class="email-compose-form">
          <div class="form-group mb-3">
            <label class="block text-sm font-medium text-gray-700 mb-1">To:</label>
            <input type="text" class="w-full p-2 border border-gray-300 rounded" placeholder="<EMAIL>">
          </div>
          <div class="form-group mb-3">
            <label class="block text-sm font-medium text-gray-700 mb-1">Subject:</label>
            <input type="text" class="w-full p-2 border border-gray-300 rounded" placeholder="Email subject">
          </div>
          <div class="form-group mb-3">
            <label class="block text-sm font-medium text-gray-700 mb-1">Message:</label>
            <textarea class="w-full p-2 border border-gray-300 rounded h-32" placeholder="Type your message here..."></textarea>
          </div>
          <div class="form-actions">
            <button class="p-button p-button-sm p-button-primary">
              <i class="pi pi-send mr-1"></i>
              Send Email
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
