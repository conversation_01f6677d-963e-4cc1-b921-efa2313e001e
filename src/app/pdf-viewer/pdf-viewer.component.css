.pdf-viewer-container {
  @apply bg-white overflow-hidden;
  width: 100%; /* Take full width of parent */
  height: calc(100vh - 8rem); /* Fixed height accounting for header, actions bar, and spacer */
  max-height: calc(100vh - 8rem); /* Match height to prevent expansion */
  flex: 0 0 auto; /* Prevent growing or shrinking */
  display: flex; /* Use flex display for better control */
  flex-direction: column; /* Stack children vertically */
  max-width: 100%; /* Prevent overflow */
  position: relative; /* For absolute positioning of children if needed */
  border-radius: 0 !important; /* Remove rounded corners */
}
.pdf-viewer-container .pdf-viewer-header {
  @apply flex items-center justify-between flex-wrap;
  min-height: 48px;
  border-radius: 0 !important; /* Remove rounded corners */
}
@media (max-width: 768px) {
  .pdf-viewer-container {
    max-height: calc(100vh - 9rem);
    height: calc(100vh - 9rem);
  }
}
@media (max-height: 600px) {
  .pdf-viewer-container {
    max-height: 450px;
    height: 450px;
  }
}
.pdf-viewer-container .pdf-viewer-header {
  @apply flex items-center justify-between flex-wrap;
  min-height: 48px;
}
.pdf-viewer-container .pdf-viewer-header h2 {
  @apply truncate;
  max-width: 200px;
}
@media (max-width: 1200px) {
  .pdf-viewer-container .pdf-viewer-header h2 {
    max-width: 150px;
  }
}
.pdf-viewer-container .pdf-viewer-header .document-status {
  @apply ml-2 inline-flex items-center;
}
.pdf-viewer-container .pdf-viewer-header .document-status .status-indicator {
  @apply inline-flex items-center justify-center h-3 w-3 rounded-full ml-2;
}
.pdf-viewer-container .pdf-viewer-header .document-status .status-indicator.status-marked {
  @apply bg-green-400;
}
.pdf-viewer-container .pdf-viewer-header .document-status .status-indicator.status-flagged {
  @apply bg-red-400;
}
.pdf-viewer-container .pdf-viewer-header .flex-container {
  display: flex;
  flex-wrap: wrap;
}
@media (max-width: 1100px) {
  .pdf-viewer-container .pdf-viewer-header .action-text {
    display: none;
  }
}
.pdf-viewer-container .pdf-viewer-header .action-dropdowns-container {
  @apply flex items-center gap-2;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container {
  @apply relative;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container.dropdown-open .dropdown-menu, .pdf-viewer-container .pdf-viewer-header .dropdown-container:hover .dropdown-menu,
.pdf-viewer-container .pdf-viewer-header .dropdown-container .dropdown-menu:hover {
  @apply opacity-100 visible transform-none;
  pointer-events: auto;
  transition-delay: 0s;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container.dropdown-right .dropdown-menu {
  left: auto;
  right: 0;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container.dropdown-right .dropdown-menu::before {
  left: auto;
  right: 20px;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container .dropdown-trigger {
  @apply rounded-full px-3 py-1.5;
  transition: all 0.2s ease;
  z-index: 1;
  position: relative;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container .dropdown-trigger.p-button-success {
  @apply bg-green-500 border-green-500 text-white;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container .dropdown-trigger.p-button-success:hover {
  @apply bg-green-600 border-green-600 shadow-md;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container .dropdown-trigger.p-button-primary {
  @apply bg-blue-600 border-blue-600 text-white;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container .dropdown-trigger.p-button-primary:hover {
  @apply bg-blue-700 border-blue-700 shadow-md;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container .dropdown-trigger.p-button-help {
  @apply bg-purple-500 border-purple-500 text-white;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container .dropdown-trigger.p-button-help:hover {
  @apply bg-purple-600 border-purple-600 shadow-md;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-container .dropdown-trigger i {
  margin-right: 0.25rem;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-menu {
  @apply absolute left-0 top-full mt-2 p-3 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-blue-100;
  min-width: 200px;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: opacity 0.2s ease, visibility 0s linear 0.3s, transform 0.2s ease;
  pointer-events: none;
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);
}
.pdf-viewer-container .pdf-viewer-header .dropdown-menu:hover, .dropdown-container:hover .pdf-viewer-container .pdf-viewer-header .dropdown-menu {
  transition-delay: 0s;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-menu::before {
  content: "";
  position: absolute;
  top: -6px;
  left: 20px;
  width: 12px;
  height: 12px;
  background: white;
  transform: rotate(45deg);
  border-left: 1px solid rgba(59, 130, 246, 0.2);
  border-top: 1px solid rgba(59, 130, 246, 0.2);
}
.pdf-viewer-container .pdf-viewer-header .dropdown-menu::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(to bottom right, rgba(59, 130, 246, 0.05), transparent);
  border-radius: inherit;
  pointer-events: none;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-header {
  @apply text-xs font-medium text-blue-700 px-2 py-1.5 mb-2 border-b border-blue-100;
  display: flex;
  align-items: center;
}
.pdf-viewer-container .pdf-viewer-header .dropdown-buttons {
  @apply flex flex-wrap gap-2 justify-center;
}
.pdf-viewer-container .pdf-viewer-header button.p-button {
  @apply flex items-center justify-center shadow-sm rounded-full;
  min-width: unset;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-rounded {
  @apply w-9 h-9;
}
.pdf-viewer-container .pdf-viewer-header button.p-button:hover {
  @apply shadow-md transform scale-110;
}
.pdf-viewer-container .pdf-viewer-header button.p-button:active {
  @apply transform scale-95;
}
.pdf-viewer-container .pdf-viewer-header button.p-button i {
  @apply text-sm;
  margin-right: 0 !important;
  position: relative;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-success {
  @apply bg-green-500 border-green-500 text-white;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-success:hover {
  @apply bg-green-600 border-green-600;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-primary {
  @apply bg-blue-600 border-blue-600 text-white;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-primary:hover {
  @apply bg-blue-700 border-blue-700;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-secondary {
  @apply bg-gray-600 border-gray-600 text-white;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-secondary:hover {
  @apply bg-gray-700 border-gray-700;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-info {
  @apply bg-cyan-500 border-cyan-500 text-white;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-info:hover {
  @apply bg-cyan-600 border-cyan-600;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-warning {
  @apply bg-amber-500 border-amber-500 text-white;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-warning:hover {
  @apply bg-amber-600 border-amber-600;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-help {
  @apply bg-purple-500 border-purple-500 text-white;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-help:hover {
  @apply bg-purple-600 border-purple-600;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-danger {
  @apply bg-red-500 border-red-500 text-white;
}
.pdf-viewer-container .pdf-viewer-header button.p-button.p-button-danger:hover {
  @apply bg-red-600 border-red-600;
}
.pdf-viewer-container .pdf-viewer-header .refresh-btn {
  @apply bg-white text-blue-600 border-blue-300;
}
.pdf-viewer-container .pdf-viewer-header .refresh-btn:hover {
  @apply bg-blue-50 border-blue-400 text-blue-700;
}
.pdf-viewer-container .pdf-viewer-header .close-btn {
  @apply ml-auto;
}
.pdf-viewer-container .pdf-viewer-tabs {
  @apply bg-white;
}
.pdf-viewer-container .pdf-viewer-tabs .tabs-container {
  display: flex;
  flex-wrap: wrap;
}
.pdf-viewer-container .pdf-viewer-tabs .tab-button {
  @apply px-4 py-2 text-sm font-medium text-blue-700 border-b-2 border-transparent;
  transition: all 0.2s ease;
  flex-shrink: 0;
  min-width: 100px;
  text-align: center;
}
@media (max-width: 1200px) {
  .pdf-viewer-container .pdf-viewer-tabs .tab-button {
    @apply px-3;
    min-width: 80px;
  }
}
.pdf-viewer-container .pdf-viewer-tabs .tab-button:hover {
  @apply bg-blue-50 text-blue-800;
}
.pdf-viewer-container .pdf-viewer-tabs .tab-button.active {
  @apply border-blue-600 text-blue-800 bg-blue-50;
}
@media (max-width: 1100px) {
  .pdf-viewer-container .pdf-viewer-tabs .tab-button i {
    margin-right: 0;
  }
}
.pdf-viewer-container .pdf-viewer-content {
  @apply bg-gray-100 rounded-b-2xl overflow-hidden;
  height: calc(100% - 88px);
  flex: 0 0 auto;
  position: relative;
  max-height: calc(100% - 88px);
}
.pdf-viewer-container .pdf-viewer-content .tab-content {
  @apply absolute inset-0 overflow-y-auto;
  height: 100%;
  width: 100%;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s 0.1s, opacity 0.1s ease;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.4) rgba(219, 234, 254, 0.3);
}
.pdf-viewer-container .pdf-viewer-content .tab-content.active {
  visibility: visible;
  opacity: 1;
  transition: visibility 0s, opacity 0.1s ease;
}
.pdf-viewer-container .pdf-viewer-content .tab-content::-webkit-scrollbar {
  @apply w-2;
}
.pdf-viewer-container .pdf-viewer-content .tab-content::-webkit-scrollbar-track {
  @apply bg-blue-50 rounded-full;
}
.pdf-viewer-container .pdf-viewer-content .tab-content::-webkit-scrollbar-thumb {
  @apply bg-blue-300 rounded-full;
}
.pdf-viewer-container .pdf-viewer-content .tab-content::-webkit-scrollbar-thumb:hover {
  @apply bg-blue-400;
}
.pdf-viewer-container .pdf-viewer-content .pdf-document-content {
  @apply flex flex-col;
}
.pdf-viewer-container .pdf-viewer-content .pdf-scroll-container {
  @apply overflow-auto;
  height: 100%;
  width: 100%;
  scroll-behavior: smooth;
}
.pdf-viewer-container .pdf-viewer-content .tab-scrollable-content {
  padding: 1rem;
  max-height: 100%;
}
.pdf-viewer-container .pdf-viewer-content iframe {
  @apply bg-white;
  border-radius: 0;
  width: 100%;
  min-height: 100%;
  display: block;
}
.pdf-viewer-container .pdf-viewer-content iframe:not([style*=height]) {
  height: 100%;
}
.pdf-viewer-container .pdf-viewer-content .event-list {
  @apply space-y-4;
}
.pdf-viewer-container .pdf-viewer-content .event-list .event-item {
  @apply flex items-start p-3 border-b border-gray-100;
}
.pdf-viewer-container .pdf-viewer-content .event-list .event-item .event-icon {
  @apply mr-3 mt-1;
}
.pdf-viewer-container .pdf-viewer-content .event-list .event-item .event-details {
  @apply flex-1;
}
.pdf-viewer-container .pdf-viewer-content .event-list .event-item .event-details .event-title {
  @apply font-medium text-gray-800;
}
.pdf-viewer-container .pdf-viewer-content .event-list .event-item .event-details .event-time {
  @apply text-xs text-gray-500 mt-1;
}
.pdf-viewer-container .pdf-viewer-content .event-list .event-item .event-details .event-description {
  @apply text-sm text-gray-600 mt-1;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list {
  @apply space-y-4;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list .transaction-item {
  @apply flex items-start p-3 border-b border-gray-100;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list .transaction-item .transaction-icon {
  @apply mr-3 mt-1;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list .transaction-item .transaction-details {
  @apply flex-1;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list .transaction-item .transaction-details .transaction-title {
  @apply font-medium text-gray-800;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list .transaction-item .transaction-details .transaction-amount {
  @apply text-sm font-bold text-green-600 mt-1;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list .transaction-item .transaction-details .transaction-time {
  @apply text-xs text-gray-500 mt-1;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list .transaction-item .transaction-details .transaction-status {
  @apply text-xs mt-1 inline-block px-2 py-0.5 rounded;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list .transaction-item .transaction-details .transaction-status:not(.pending) {
  @apply bg-green-100 text-green-800;
}
.pdf-viewer-container .pdf-viewer-content .transaction-list .transaction-item .transaction-details .transaction-status.pending {
  @apply bg-yellow-100 text-yellow-800;
}
.pdf-viewer-container .pdf-viewer-content .email-list {
  @apply space-y-4;
}
.pdf-viewer-container .pdf-viewer-content .email-list .email-item {
  @apply flex items-start p-3 border-b border-gray-100;
}
.pdf-viewer-container .pdf-viewer-content .email-list .email-item .email-icon {
  @apply mr-3 mt-1;
}
.pdf-viewer-container .pdf-viewer-content .email-list .email-item .email-details {
  @apply flex-1;
}
.pdf-viewer-container .pdf-viewer-content .email-list .email-item .email-details .email-subject {
  @apply font-medium text-gray-800;
}
.pdf-viewer-container .pdf-viewer-content .email-list .email-item .email-details .email-sender {
  @apply text-sm text-gray-600 mt-1;
}
.pdf-viewer-container .pdf-viewer-content .email-list .email-item .email-details .email-time {
  @apply text-xs text-gray-500 mt-1;
}
.pdf-viewer-container .pdf-viewer-content .email-list .email-item .email-details .email-preview {
  @apply text-sm text-gray-600 mt-2 italic;
}
.pdf-viewer-container .crossed-text {
  text-decoration: line-through;
  text-decoration-thickness: 2px;
  text-decoration-color: rgba(37, 99, 235, 0.5);
  color: #60a5fa; /* blue-400 equivalent */
}
.pdf-viewer-container .red-text {
  color: #ef4444; /* red-500 equivalent */
  font-weight: 500; /* medium equivalent */
}
.pdf-viewer-container .crossed-text.red-text {
  color: #f87171; /* red-400 equivalent */
  text-decoration-color: rgba(239, 68, 68, 0.8);
}

.loading-spinner-large {
  @apply inline-block rounded-full h-12 w-12;
  border: 3px solid rgba(37, 99, 235, 0.2);
  border-top-color: rgba(37, 99, 235, 0.9);
  animation: spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.loading-text {
  @apply text-lg font-medium text-blue-700;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.pdf-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.4) rgba(219, 234, 254, 0.3);
}
.pdf-scroll-container::-webkit-scrollbar {
  @apply w-2;
}
.pdf-scroll-container::-webkit-scrollbar-track {
  @apply bg-blue-50 rounded-full;
}
.pdf-scroll-container::-webkit-scrollbar-thumb {
  @apply bg-blue-300 rounded-full;
}
.pdf-scroll-container::-webkit-scrollbar-thumb:hover {
  @apply bg-blue-400;
}

.tab-content {
  overflow-y: scroll !important;
  scrollbar-width: thin !important;
  transition: opacity 0.15s ease-in-out;
  will-change: opacity, visibility;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100% !important;
  max-height: 100% !important;
}
.tab-content::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
  display: block !important;
}
.tab-content > div {
  width: calc(100% - 8px);
  max-width: calc(100% - 8px);
}

/*# sourceMappingURL=pdf-viewer.component.css.map */
