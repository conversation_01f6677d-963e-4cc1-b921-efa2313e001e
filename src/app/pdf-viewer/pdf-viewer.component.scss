// PDF Viewer styles
.pdf-viewer-container {
  @apply bg-white overflow-hidden;
  width: 100%; /* Take full width of parent */
  height: calc(100vh - 8rem); /* Fixed height accounting for header, actions bar, and spacer */
  max-height: calc(100vh - 8rem); /* Match height to prevent expansion */
  flex: 0 0 auto; /* Prevent growing or shrinking */
  display: flex; /* Use flex display for better control */
  flex-direction: column; /* Stack children vertically */
  max-width: 100%; /* Prevent overflow */
  position: relative; /* For absolute positioning of children if needed */
  border-radius: 0 !important; /* Remove rounded corners */

  .pdf-viewer-header {
    @apply flex items-center justify-between flex-wrap;
    min-height: 48px;
    border-radius: 0 !important; /* Remove rounded corners */
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    max-height: calc(100vh - 9rem); // Adjust for mobile screens
    height: calc(100vh - 9rem);
  }

  @media (max-height: 600px) {
    max-height: 450px; // Set a reasonable fixed height for very small screens
    height: 450px;
  }

  .pdf-viewer-header {
    @apply flex items-center justify-between flex-wrap;
    min-height: 48px;

    h2 {
      @apply truncate;
      max-width: 200px;

      @media (max-width: 1200px) {
        max-width: 150px;
      }
    }

    // Document status indicators
    .document-status {
      @apply ml-2 inline-flex items-center;

      .status-indicator {
        @apply inline-flex items-center justify-center h-3 w-3 rounded-full ml-2;

        &.status-marked {
          @apply bg-green-400;
        }

        &.status-flagged {
          @apply bg-red-400;
        }
      }
    }

    .flex-container {
      display: flex;
      flex-wrap: wrap;
    }

    .action-text {
      @media (max-width: 1100px) {
        display: none;
      }
    }

    // Action dropdowns container
    .action-dropdowns-container {
      @apply flex items-center gap-2;
    }

    // Dropdown container
    .dropdown-container {
      @apply relative;

      &.dropdown-open .dropdown-menu,
      &:hover .dropdown-menu,
      .dropdown-menu:hover {
        @apply opacity-100 visible transform-none;
        pointer-events: auto;
        transition-delay: 0s;
      }

      // Right-aligned dropdown
      &.dropdown-right .dropdown-menu {
        left: auto;
        right: 0;

        &::before {
          left: auto;
          right: 20px;
        }
      }

      .dropdown-trigger {
        @apply rounded-full px-3 py-1.5;
        transition: all 0.2s ease;
        z-index: 1;
        position: relative;

        &.p-button-success {
          @apply bg-green-500 border-green-500 text-white;

          &:hover {
            @apply bg-green-600 border-green-600 shadow-md;
          }
        }

        &.p-button-primary {
          @apply bg-blue-600 border-blue-600 text-white;

          &:hover {
            @apply bg-blue-700 border-blue-700 shadow-md;
          }
        }

        &.p-button-help {
          @apply bg-purple-500 border-purple-500 text-white;

          &:hover {
            @apply bg-purple-600 border-purple-600 shadow-md;
          }
        }

        i {
          margin-right: 0.25rem;
        }
      }
    }

    // Dropdown menu styling
    .dropdown-menu {
      @apply absolute left-0 top-full mt-2 p-3 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-blue-100;
      min-width: 200px;
      z-index: 100;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: opacity 0.2s ease, visibility 0s linear 0.3s, transform 0.2s ease;
      pointer-events: none;
      box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.1), 0 8px 10px -6px rgba(59, 130, 246, 0.1);

      // When visible, remove the delay
      &:hover, .dropdown-container:hover & {
        transition-delay: 0s;
      }

      &::before {
        content: '';
        position: absolute;
        top: -6px;
        left: 20px;
        width: 12px;
        height: 12px;
        background: white;
        transform: rotate(45deg);
        border-left: 1px solid rgba(59, 130, 246, 0.2);
        border-top: 1px solid rgba(59, 130, 246, 0.2);
      }

      &::after {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(to bottom right, rgba(59, 130, 246, 0.05), transparent);
        border-radius: inherit;
        pointer-events: none;
      }
    }

    // Dropdown header
    .dropdown-header {
      @apply text-xs font-medium text-blue-700 px-2 py-1.5 mb-2 border-b border-blue-100;
      display: flex;
      align-items: center;
    }

    // Dropdown buttons container
    .dropdown-buttons {
      @apply flex flex-wrap gap-2 justify-center;
    }

    // Common button styling
    button.p-button {
      @apply flex items-center justify-center shadow-sm rounded-full;
      min-width: unset;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;

      &.p-button-rounded {
        @apply w-9 h-9;
      }

      &:hover {
        @apply shadow-md transform scale-110;
      }

      &:active {
        @apply transform scale-95;
      }

      i {
        @apply text-sm;
        margin-right: 0 !important;
        position: relative;
      }

      // Button variants
      &.p-button-success {
        @apply bg-green-500 border-green-500 text-white;

        &:hover {
          @apply bg-green-600 border-green-600;
        }
      }

      &.p-button-primary {
        @apply bg-blue-600 border-blue-600 text-white;

        &:hover {
          @apply bg-blue-700 border-blue-700;
        }
      }

      &.p-button-secondary {
        @apply bg-gray-600 border-gray-600 text-white;

        &:hover {
          @apply bg-gray-700 border-gray-700;
        }
      }

      &.p-button-info {
        @apply bg-cyan-500 border-cyan-500 text-white;

        &:hover {
          @apply bg-cyan-600 border-cyan-600;
        }
      }

      &.p-button-warning {
        @apply bg-amber-500 border-amber-500 text-white;

        &:hover {
          @apply bg-amber-600 border-amber-600;
        }
      }

      &.p-button-help {
        @apply bg-purple-500 border-purple-500 text-white;

        &:hover {
          @apply bg-purple-600 border-purple-600;
        }
      }

      &.p-button-danger {
        @apply bg-red-500 border-red-500 text-white;

        &:hover {
          @apply bg-red-600 border-red-600;
        }
      }
    }

    // Specific button styling overrides
    .refresh-btn {
      @apply bg-white text-blue-600 border-blue-300;

      &:hover {
        @apply bg-blue-50 border-blue-400 text-blue-700;
      }
    }

    .close-btn {
      @apply ml-auto;
    }

    // End of button styling
  }

  .pdf-viewer-tabs {
    @apply bg-white;

    .tabs-container {
      display: flex;
      flex-wrap: wrap;
    }

    .tab-button {
      @apply px-4 py-2 text-sm font-medium text-blue-700 border-b-2 border-transparent;
      transition: all 0.2s ease;
      flex-shrink: 0;
      min-width: 100px;
      text-align: center;

      @media (max-width: 1200px) {
        @apply px-3;
        min-width: 80px;
      }

      &:hover {
        @apply bg-blue-50 text-blue-800;
      }

      &.active {
        @apply border-blue-600 text-blue-800 bg-blue-50;
      }

      i {
        @media (max-width: 1100px) {
          margin-right: 0;
        }
      }
    }
  }

  .pdf-viewer-content {
    @apply bg-gray-100 rounded-b-2xl overflow-hidden;
    height: calc(100% - 88px); // Subtract header and tabs height
    flex: 0 0 auto; // Prevent growing or shrinking
    position: relative; // For absolute positioning of tab content
    max-height: calc(100% - 88px); // Ensure consistent height

    // Common styles for all tab content
    .tab-content {
      @apply absolute inset-0 overflow-y-auto;
      height: 100%; // Full height of parent
      width: 100%; // Full width of parent
      visibility: hidden; // Hide by default
      opacity: 0; // Hide by default
      transition: visibility 0s 0.1s, opacity 0.1s ease; // Smooth transition
      scroll-behavior: smooth; // Enable smooth scrolling
      scrollbar-width: thin;
      scrollbar-color: rgba(59, 130, 246, 0.4) rgba(219, 234, 254, 0.3);

      // Show active tab
      &.active {
        visibility: visible;
        opacity: 1;
        transition: visibility 0s, opacity 0.1s ease;
      }

      // Scrollbar styling
      &::-webkit-scrollbar {
        @apply w-2;
      }

      &::-webkit-scrollbar-track {
        @apply bg-blue-50 rounded-full;
      }

      &::-webkit-scrollbar-thumb {
        @apply bg-blue-300 rounded-full;

        &:hover {
          @apply bg-blue-400;
        }
      }
    }

    // Special styling for the PDF document tab
    .pdf-document-content {
      @apply flex flex-col;
    }

    // Scrollable container for PDF
    .pdf-scroll-container {
      @apply overflow-auto;
      height: 100%;
      width: 100%;
      scroll-behavior: smooth; // Enable smooth scrolling
    }

    // Scrollable content for other tabs
    .tab-scrollable-content {
      padding: 1rem; // Add padding
      max-height: 100%; // Use parent's height constraint
    }

    iframe {
      @apply bg-white;
      border-radius: 0; // Remove any potential border radius
      width: 100%;
      min-height: 100%;
      display: block; // Ensure it takes full width

      // Prevent iframe from expanding the container
      &:not([style*="height"]) {
        height: 100%;
      }
    }

    // Event styles
    .event-list {
      @apply space-y-4;

      .event-item {
        @apply flex items-start p-3 border-b border-gray-100;

        .event-icon {
          @apply mr-3 mt-1;
        }

        .event-details {
          @apply flex-1;

          .event-title {
            @apply font-medium text-gray-800;
          }

          .event-time {
            @apply text-xs text-gray-500 mt-1;
          }

          .event-description {
            @apply text-sm text-gray-600 mt-1;
          }
        }
      }
    }

    // Transaction styles
    .transaction-list {
      @apply space-y-4;

      .transaction-item {
        @apply flex items-start p-3 border-b border-gray-100;

        .transaction-icon {
          @apply mr-3 mt-1;
        }

        .transaction-details {
          @apply flex-1;

          .transaction-title {
            @apply font-medium text-gray-800;
          }

          .transaction-amount {
            @apply text-sm font-bold text-green-600 mt-1;
          }

          .transaction-time {
            @apply text-xs text-gray-500 mt-1;
          }

          .transaction-status {
            @apply text-xs mt-1 inline-block px-2 py-0.5 rounded;
            &:not(.pending) {
              @apply bg-green-100 text-green-800;
            }
            &.pending {
              @apply bg-yellow-100 text-yellow-800;
            }
          }
        }
      }
    }

    // Email styles
    .email-list {
      @apply space-y-4;

      .email-item {
        @apply flex items-start p-3 border-b border-gray-100;

        .email-icon {
          @apply mr-3 mt-1;
        }

        .email-details {
          @apply flex-1;

          .email-subject {
            @apply font-medium text-gray-800;
          }

          .email-sender {
            @apply text-sm text-gray-600 mt-1;
          }

          .email-time {
            @apply text-xs text-gray-500 mt-1;
          }

          .email-preview {
            @apply text-sm text-gray-600 mt-2 italic;
          }
        }
      }
    }
  }

  // Text styles for document status
  .crossed-text {
    text-decoration: line-through;
    text-decoration-thickness: 2px;
    text-decoration-color: rgba(37, 99, 235, 0.5);
    color: #60a5fa; /* blue-400 equivalent */
  }

  .red-text {
    color: #ef4444; /* red-500 equivalent */
    font-weight: 500; /* medium equivalent */
  }

  .crossed-text.red-text {
    color: #f87171; /* red-400 equivalent */
    text-decoration-color: rgba(239, 68, 68, 0.8);
  }
}

// Loading spinner animation
.loading-spinner-large {
  @apply inline-block rounded-full h-12 w-12;
  border: 3px solid rgba(37, 99, 235, 0.2);
  border-top-color: rgba(37, 99, 235, 0.9);
  animation: spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  @apply text-lg font-medium text-blue-700;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Custom scrollbar styling for PDF viewer
.pdf-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.4) rgba(219, 234, 254, 0.3);

  &::-webkit-scrollbar {
    @apply w-2;
  }

  &::-webkit-scrollbar-track {
    @apply bg-blue-50 rounded-full;
  }

  &::-webkit-scrollbar-thumb {
    @apply bg-blue-300 rounded-full;

    &:hover {
      @apply bg-blue-400;
    }
  }
}

// Prevent content from causing layout shifts
.tab-content {
  // Force scrollbar to always be visible to prevent layout shifts
  overflow-y: scroll !important; // Always show scrollbar
  scrollbar-width: thin !important; // For Firefox

  &::-webkit-scrollbar {
    width: 8px !important; // Fixed width
    height: 8px !important; // For horizontal scrollbar if needed
    display: block !important; // Always show scrollbar
  }

  // Ensure content doesn't cause container to resize
  & > div {
    width: calc(100% - 8px); // Account for scrollbar width
    max-width: calc(100% - 8px); // Ensure content doesn't overflow
  }

  // Ensure smooth transitions between tabs
  transition: opacity 0.15s ease-in-out;
  will-change: opacity, visibility;

  // Prevent content from causing container to resize during tab switching
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  // Ensure fixed height
  height: 100% !important;
  max-height: 100% !important;
}
