import {Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DocumentProvider } from '../../providers/document.provider';
import { Subscription } from 'rxjs';
import { DocumentId } from '../../providers/document-id';
import { TabsModule } from 'primeng/tabs';
import { ButtonModule } from 'primeng/button';
import { PdfViewerComponent } from '../pdf-viewer/pdf-viewer.component';
import { FileSystemViewMode } from '../../providers/file-system-view.provider';
import { WebApiClient } from '../../client/client';

interface TabDocument {
  id: DocumentId;
  title: string;
  path: string;
}

@Component({
  selector: 'app-pdf-viewer-tabs',
  standalone: true,
  imports: [
    CommonModule,
    TabsModule,
    ButtonModule,
    PdfViewerComponent
  ],
  templateUrl: './pdf-viewer-tabs.component.html',
  styleUrl: './pdf-viewer-tabs.component.scss'
})
export class PdfViewerTabsComponent implements OnInit, OnDestroy, OnChanges {
  @Input() selectedFilePath: string | null = null;
  @Output() close = new EventEmitter<void>();

  private readonly subscription = new Subscription();
  tabs: TabDocument[] = [];

  constructor(
    private readonly documentProvider: DocumentProvider,
  ) {
    this.subscription.add(
      this.documentProvider.onDocumentAdded.subscribe(documentId => {
        this.addTab(documentId);
      })
    );

    this.subscription.add(
      this.documentProvider.onDocumentRemoved.subscribe(documentId => {
        this.removeTab(documentId);
      })
    );
  }

  ngOnInit(): void {

  }

  ngOnChanges(): void {
    if (this.selectedFilePath) {
      this.openDocument(this.selectedFilePath);
    }
  }

  openDocument(filePath: string): void {
    const existingTab = this.tabs.find(tab => tab.path === filePath);
    if (existingTab) {
      this._activeTabId = existingTab.id.toString();
    } else {
      const documentId = new DocumentId(filePath, 'date' as FileSystemViewMode);
      this.documentProvider.addDocumentIfDoesntExist(documentId, { localPath: filePath });
    }
  }

  addTab(documentId: DocumentId): void {
    const fileName = documentId.path.split('/').pop() || 'Document';
    this.tabs.push({
      id: documentId,
      title: fileName,
      path: documentId.path
    });
    this._activeTabId = documentId.toString();
  }

  removeTab(documentId: DocumentId): void {
    const index = this.tabs.findIndex(tab => tab.id.toString() === documentId.toString());
    const wasActive = this.tabs[index]?.id.toString() === this._activeTabId;

    if (index >= 0) {
      this.tabs.splice(index, 1);

      // If we removed the active tab, activate another one
      if (wasActive) {
        if (this.tabs.length > 0) {
          // If there are still tabs, activate the previous one or the first one
          const newIndex = Math.max(0, index - 1);
          this._activeTabId = this.tabs[newIndex].id.toString();
        } else {
          // If no tabs left, close the viewer
          this._activeTabId = '';
          this.close.emit();
        }
      }
    }
  }

  get activeTabAsString(): string | number {
    const activeTab = this.tabs.find(tab => tab.id.toString() === this._activeTabId);
    return activeTab?.id.toString() || '';
  }

  set activeTabAsString(value: string | number | null) {
    if (value == null || (typeof value !== 'string' && typeof value !== 'number')) {
      return;
    }

    const valueStr = typeof value === 'number' ? value.toString() : value;
    const tab = this.tabs.find(tab => tab.id.toString() === valueStr);

    if (tab) {
      this._activeTabId = valueStr;
    }
  }

  private _activeTabId: string = '';

  getTabIndex(documentId: DocumentId): number {
    return this.tabs.findIndex(tab => tab.id.toString() === documentId.toString());
  }

  /**
   * Returns a shortened version of the path for display in the UI
   * Shows only the last part of the path with a prefix
   */
  getShortPath(path: string): string {
    if (!path) return '';

    const parts = path.split('/');
    if (parts.length <= 1) return path;

    // Get the last directory and filename
    const lastDir = parts.length > 2 ? parts[parts.length - 2] : '';
    const fileName = parts[parts.length - 1];

    if (lastDir) {
      return `.../${lastDir}/${fileName}`;
    }

    return `.../${fileName}`;
  }

  closeTab(index: number, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }

    if (index >= 0 && index < this.tabs.length) {
      const documentId = this.tabs[index].id;
      this.documentProvider.deleteDocument(documentId);
      // The tab will be removed via the subscription to onDocumentRemoved
    }
  }

  closeAllTabs(): void {
    // Make a copy of the tabs array to avoid modification during iteration
    const tabsCopy = [...this.tabs];
    tabsCopy.forEach(tab => {
      this.documentProvider.deleteDocument(tab.id);
    });

    // After all tabs are closed, emit the close event
    if (tabsCopy.length > 0) {
      this.close.emit();
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
