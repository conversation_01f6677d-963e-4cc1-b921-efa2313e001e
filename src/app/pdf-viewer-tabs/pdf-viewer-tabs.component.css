.pdf-viewer-tabs-container {
  @apply flex flex-col;
  height: 100%;
  width: 100%;
  background-color: white;
  overflow: hidden;
}
.pdf-viewer-tabs-container .pdf-tabs-header {
  @apply flex items-center justify-between p-2 border-b border-blue-300 bg-gradient-to-r from-blue-100 to-blue-50 rounded-t-2xl;
  /* Document tabs styling - matching search tabs */
}
.pdf-viewer-tabs-container .pdf-tabs-header .document-tabs {
  @apply sticky top-0 z-20 bg-white;
  margin: 0;
  padding: 0;
  flex-grow: 1;
}
.pdf-viewer-tabs-container .pdf-tabs-header .document-tabs ::ng-deep .p-tablist {
  @apply bg-white border-b border-blue-200 shadow-sm;
  margin: 0;
  padding: 0;
}
.pdf-viewer-tabs-container .pdf-tabs-header .document-tabs ::ng-deep .p-tab {
  @apply py-1 px-4 text-sm font-medium text-blue-700 hover:text-blue-900 transition-colors;
  height: 2.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.pdf-viewer-tabs-container .pdf-tabs-header .document-tabs ::ng-deep .p-tab:hover {
  @apply bg-blue-50;
}
.pdf-viewer-tabs-container .pdf-tabs-header .document-tabs ::ng-deep .p-tab.p-highlight {
  @apply text-blue-900 bg-blue-100/50 border-b-2 border-blue-500;
}
.pdf-viewer-tabs-container .pdf-tabs-header .document-tabs ::ng-deep .p-tablist-active-bar {
  @apply bg-blue-500;
}
.pdf-viewer-tabs-container .pdf-tabs-header .tab-actions {
  @apply flex items-center ml-2;
}
.pdf-viewer-tabs-container .pdf-tabs-header .tab-actions button {
  @apply text-blue-600 hover:text-blue-800 hover:bg-blue-100;
}
.pdf-viewer-tabs-container .pdf-tabs-content {
  @apply flex-grow relative overflow-hidden;
}
.pdf-viewer-tabs-container .pdf-tabs-content .tab-content {
  @apply absolute inset-0 opacity-0 invisible;
  transition: opacity 0.2s ease, visibility 0s 0.2s;
}
.pdf-viewer-tabs-container .pdf-tabs-content .tab-content.active {
  @apply opacity-100 visible;
  transition: opacity 0.2s ease, visibility 0s;
}
.pdf-viewer-tabs-container .pdf-tabs-content .tab-content .pdf-viewer-in-tab {
  @apply block h-full w-full;
}

/* Tab close button - matching file-system component */
.tab-close-btn {
  @apply flex items-center justify-center w-5 h-5 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-800;
  transition: all 0.2s ease;
  z-index: 2 !important;
}
.tab-close-btn:hover {
  @apply transform rotate-90;
}
.tab-close-btn i {
  @apply text-xs;
}

/* Document ID badge styling - similar to viewmode-badge */
.document-id-badge {
  @apply inline-flex items-center justify-center px-1.5 py-0.5 ml-1.5 text-xs font-medium rounded;
  background-color: #3b82f6;
  color: white;
  transition: transform 0.15s ease;
  position: relative;
  text-align: center;
  font-size: 0.7rem;
  line-height: 1;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.document-id-badge:hover {
  transform: translateY(-1px);
}

/* Document tabs wrapper */
.document-tabs-wrapper {
  @apply flex-grow overflow-hidden;
}

/* Modern styling for the tabs */
::ng-deep {
  /* Ensure tab buttons have proper z-index */
}
::ng-deep .p-tablist {
  @apply rounded-t-lg overflow-hidden;
  height: auto !important;
}
::ng-deep .p-tab {
  @apply rounded-t-lg;
  height: 2rem;
  min-height: 2rem;
  padding-top: 0;
  padding-bottom: 0;
}
::ng-deep .p-tab.p-highlight {
  @apply shadow-sm;
}
::ng-deep .p-tab > .p-button-text {
  z-index: 5;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

/*# sourceMappingURL=pdf-viewer-tabs.component.css.map */
