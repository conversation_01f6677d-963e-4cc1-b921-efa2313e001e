<div class="pdf-viewer-tabs-container shadow-xl rounded-2xl ring-1 ring-blue-300 flex flex-col">
  <!-- Tabs as the main header -->
  <p-tabs *ngIf="tabs.length > 0"
          [scrollable]="true"
          [(value)]="activeTabAsString"
          class="document-tabs">
    <p-tablist>
      <!-- Document tabs -->
      <ng-container *ngFor="let tab of tabs; let i = index">
        <p-tab [value]="tab.id.toString()" class="flex items-center !gap-2">
          <i class="pi pi-file-pdf text-red-600 mr-2"></i>
          <span class="font-medium">{{ tab.title }}</span>
          <span class="document-id-badge" title="{{ tab.id.path }}">{{ getShortPath(tab.id.path) }}</span>
          <button
            class="tab-close-btn ml-2"
            (pointerdown)="$event.stopPropagation()"
            (click)="$event.stopPropagation(); closeTab(i);">
            <i class="pi pi-times"></i>
          </button>
        </p-tab>
      </ng-container>
    </p-tablist>
  </p-tabs>

  <!-- PDF Viewer Content -->
  <div class="pdf-tabs-content">
    <ng-container *ngFor="let tab of tabs">
      <div class="tab-content" [class.active]="tab.id.toString() === activeTabAsString">
        <app-pdf-viewer
          [filePath]="tab.path"
          (close)="closeTab(getTabIndex(tab.id))"
          class="pdf-viewer-in-tab">
        </app-pdf-viewer>
      </div>
    </ng-container>
  </div>
</div>
