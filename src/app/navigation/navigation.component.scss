/* Mobile menu toggle button */
.mobile-menu-toggle {
  position: fixed;
  top: 16px;
  right: 16px;
  z-index: 1000;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
  background-color: #2563eb;
}

.mobile-menu-toggle.active {
  background-color: #1e40af;
}

.mobile-menu-toggle i {
  font-size: 1.5rem;
}

/* Mobile menu overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 90;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Basic navigation container */
.nav-container {
  height: 100vh;
  background: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  width: 70px;
  transition: width 0.3s ease, transform 0.3s ease;
  overflow: hidden;
  border-right: 1px solid #e2e8f0;
  background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
  z-index: 100;
  position: relative;
}

/* Mobile navigation styles */
.nav-container.mobile {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  transform: translateX(-100%);
  z-index: 100;
}

.nav-container.mobile-open {
  transform: translateX(0);
}

/* Expanded state - only for desktop */
.nav-container:not(.mobile).expanded,
.nav-container:not(.mobile):hover {
  width: 240px;
}

/* Header section */
.nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #bfdbfe;
  height: 70px;
  background: linear-gradient(to right, #eff6ff, #dbeafe);
}

/* Mobile close button */
.close-mobile-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 8px;
  color: #2563eb;
  background: transparent;
  border: none;
  cursor: pointer;
  min-width: 28px;
  min-height: 28px;
  transition: background-color 0.2s ease;
}

.close-mobile-btn:hover {
  background-color: #bfdbfe;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-text {
  font-weight: bold;
  font-size: 20px;
  color: #1e40af;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-container.expanded .logo-text,
.nav-container:hover .logo-text {
  opacity: 1;
}

.toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  padding: 8px;
  color: #2563eb;
  background: transparent;
  border: none;
  cursor: pointer;
  min-width: 28px;
  min-height: 28px;
  transition: background-color 0.2s ease;
}

.toggle-btn:hover {
  background-color: #bfdbfe;
}

/* Menu section */
.nav-menu {
  flex-grow: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  position: relative;
  margin-bottom: 8px;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 14px 12px;
  color: #4b5563;
  text-decoration: none;
  border-radius: 8px;
  margin: 0 4px 4px 4px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  height: 48px;
}

.menu-link:hover {
  background-color: #eff6ff;
  color: #2563eb;
}

.menu-link.active-link {
  background-color: #bfdbfe;
  color: #1e40af;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
  border-left: 3px solid #3b82f6;
}

.menu-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: absolute;
  left: 12px;
}

/* Icon colors */
.pi-home { color: #2563eb; }
.pi-file-pdf { color: #dc2626; }
.pi-folder { color: #d97706; }
.pi-chart-bar { color: #059669; }
.pi-cog { color: #3b82f6; }

.menu-label {
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  position: absolute;
  left: 48px;
  pointer-events: none;
  text-align: left;
  font-size: 1rem;
}

.nav-container.expanded .menu-label,
.nav-container:hover .menu-label,
.nav-container.mobile .menu-label {
  opacity: 1;
}

/* Submenu styles */
.submenu-trigger {
  cursor: pointer;
}

.submenu-trigger .menu-link {
  padding-left: 12px;
}

.submenu-icon {
  font-size: 0.75rem;
  position: absolute;
  right: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.nav-container.expanded .submenu-icon,
.nav-container:hover .submenu-icon,
.nav-container.mobile .submenu-icon {
  opacity: 1;
}

.submenu {
  list-style: none;
  padding-left: 28px;
  margin-top: 4px;
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease;
}

.menu-item.has-submenu.expanded .submenu {
  max-height: 500px;
}

.submenu-item {
  margin-bottom: 6px;
}

.submenu-link {
  display: flex;
  align-items: center;
  padding: 12px 12px 12px 40px;
  color: #6b7280;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  height: 42px;
}

.submenu-link:hover {
  background-color: #eff6ff;
  color: #2563eb;
}

.submenu-link.active-link {
  background-color: #bfdbfe;
  color: #1e40af;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.15);
  border-left: 2px solid #3b82f6;
}

.submenu-item .submenu-icon {
  font-size: 1.125rem;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 16px;
}

.submenu-item .submenu-label {
  left: 40px;
}

/* Footer section */
.nav-footer {
  border-top: 1px solid #e5e7eb;
  padding: 12px;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #dbeafe;
  color: #2563eb;
  border-radius: 50%;
  width: 32px;
  height: 32px;
}

.user-info {
  display: flex;
  flex-direction: column;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-container.expanded .user-info,
.nav-container:hover .user-info,
.nav-container.mobile .user-info {
  opacity: 1;
}

.user-name {
  font-weight: 500;
  color: #1f2937;
}

.user-role {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Custom scrollbar */
.nav-menu {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.nav-menu::-webkit-scrollbar {
  width: 4px;
}

.nav-menu::-webkit-scrollbar-track {
  background: transparent;
}

.nav-menu::-webkit-scrollbar-thumb {
  background-color: #bfdbfe;
  border-radius: 9999px;
}

.nav-menu::-webkit-scrollbar-thumb:hover {
  background-color: #93c5fd;
}

/* Auth-related styles */
.logout-btn {
  opacity: 0;
  transition: opacity 0.3s ease;
  margin-left: auto;

  ::ng-deep .p-button {
    background: transparent;
    border: 1px solid #e5e7eb;
    color: #6b7280;
    padding: 0.25rem;
    min-width: 28px;
    height: 28px;

    &:hover {
      background: #f3f4f6;
      border-color: #d1d5db;
      color: #374151;
    }
  }
}

.nav-container.expanded .logout-btn,
.nav-container:hover .logout-btn,
.nav-container.mobile .logout-btn {
  opacity: 1;
}

.auth-prompt {
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: #3b82f6;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-weight: 500;

  &:hover {
    background-color: #eff6ff;
    color: #2563eb;
  }
}

.login-text {
  opacity: 0;
  transition: opacity 0.3s ease;
  white-space: nowrap;
}

.nav-container.expanded .login-text,
.nav-container:hover .login-text,
.nav-container.mobile .login-text {
  opacity: 1;
}
