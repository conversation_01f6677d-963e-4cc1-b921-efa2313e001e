import { Component, OnInit, HostListener, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NavigationService, NavItem } from '../../services/navigation.service';
import { RippleModule } from 'primeng/ripple';
import { AuthService } from '../../services/auth.service';
import { Subscription } from 'rxjs';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-navigation',
  standalone: true,
  imports: [CommonModule, RouterModule, RippleModule, ButtonModule],
  templateUrl: './navigation.component.html',
  styleUrl: './navigation.component.scss'
})
export class NavigationComponent implements OnInit, OnDestroy {
  menuItems: NavItem[] = [];
  expanded = false;
  hovering = false;
  isMobileMenuOpen = false;
  isMobile = false;

  // Auth state
  isAuthenticated = false;
  currentUser: any = null;
  private subscription = new Subscription();

  constructor(
    private navigationService: NavigationService,
    private authService: AuthService
  ) {
    this.checkScreenSize();
  }

  ngOnInit(): void {
    this.menuItems = this.navigationService.getMenuItems();
    this.subscription.add(
      this.navigationService.expanded$.subscribe(expanded => {
        this.expanded = expanded;
      })
    );

    // Subscribe to auth state
    this.subscription.add(
      this.authService.authState$.subscribe(state => {
        this.isAuthenticated = state.isAuthenticated;
        this.currentUser = state.user;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  toggleExpanded(): void {
    this.navigationService.toggleExpanded();
  }

  toggleSubmenu(item: NavItem, event: Event): void {
    event.preventDefault();
    event.stopPropagation();
    item.expanded = !item.expanded;
  }

  toggleMobileMenu(): void {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
    // Add body class to prevent scrolling when menu is open
    if (this.isMobileMenuOpen) {
      document.body.classList.add('mobile-menu-open');
    } else {
      document.body.classList.remove('mobile-menu-open');
    }
  }

  closeMobileMenu(): void {
    if (this.isMobileMenuOpen) {
      this.isMobileMenuOpen = false;
      document.body.classList.remove('mobile-menu-open');
    }
  }

  @HostListener('window:resize')
  checkScreenSize(): void {
    this.isMobile = window.innerWidth < 768;
    if (!this.isMobile && this.isMobileMenuOpen) {
      this.closeMobileMenu();
    }
  }

  @HostListener('mouseenter')
  onMouseEnter(): void {
    if (!this.isMobile) {
      this.hovering = true;
    }
  }

  @HostListener('mouseleave')
  onMouseLeave(): void {
    if (!this.isMobile) {
      this.hovering = false;
    }
  }

  // Close mobile menu when clicking a link
  onNavLinkClick(): void {
    if (this.isMobile) {
      this.closeMobileMenu();
    }
  }

  // Logout functionality
  logout(): void {
    this.authService.logout();
    if (this.isMobile) {
      this.closeMobileMenu();
    }
  }
}
