.app-container {
  @apply flex h-screen w-full overflow-hidden bg-gray-50;
  position: relative;
}

.content-container {
  @apply flex-grow overflow-auto p-4;
  background: linear-gradient(to bottom, #EFF6FF, #DBEAFE);
  transition: padding 0.3s ease;

  @media (min-width: 768px) {
    padding: 1.5rem;
  }

  &.full-width {
    padding: 0;
    background: none;
    overflow: hidden;
  }
}

/* Mobile styles */
@media (max-width: 767px) {
  .app-container {
    display: block;
  }

  .content-container {
    width: 100%;
    padding-top: 80px; /* Space for the mobile menu button */
  }
}
