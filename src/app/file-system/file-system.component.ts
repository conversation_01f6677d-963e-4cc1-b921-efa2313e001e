import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {ButtonModule} from 'primeng/button';
import {InputTextModule} from 'primeng/inputtext';
import {RippleModule} from 'primeng/ripple';
import {TreeModule} from 'primeng/tree';
import {MenuModule} from 'primeng/menu';
import {CommonModule, DecimalPipe, NgForOf, NgIf} from '@angular/common';
import {LucideAngularModule} from 'lucide-angular';
import {FileSystemView} from '../../state/FileSystemView';
import {NotificationService} from '../../services/notification.service';
import {
  FileSystemViewMode,
  FileSystemViewProvider,
} from '../../providers/file-system-view.provider';
import {PdfViewerTabsComponent} from '../pdf-viewer-tabs/pdf-viewer-tabs.component';
import {FileSearchComponent} from '../file-search/file-search.component';
import {FileSystemState} from '../../state/file-system.state';
import {Subscription} from 'rxjs';
import {TabsModule} from 'primeng/tabs';
import {SearchCriteria} from '../file-search/search.criteria';
import {FileSystemViewId} from '../../providers/file-system-view.id';
import {FileActionsComponent} from '../file-actions/file-actions.component';

@Component({
  selector: 'app-file-system',
  imports: [
    NgIf,
    DecimalPipe,
    ButtonModule,
    InputTextModule,
    RippleModule,
    TreeModule,
    NgForOf,
    MenuModule,
    CommonModule,
    LucideAngularModule,
    PdfViewerTabsComponent,
    FileSearchComponent,
    TabsModule,
    FileActionsComponent
  ],
  standalone: true,
  templateUrl: './file-system.component.html',
  styleUrl: './file-system.component.scss'
})
export class FileSystemComponent implements OnInit, OnDestroy {
  private readonly subscription = new Subscription();
  readonly searchViews: FileSystemViewId[] = [];

  searchTabsVisible = true;
  searchFormVisible = false;
  selectedFilePath: string | null = null;

  model: FileSystemView | null = null;
  currentSearchCriteria: SearchCriteria = SearchCriteria.empty();

  constructor(
    private readonly notification: NotificationService,
    private readonly fileSystemViewProvider: FileSystemViewProvider,
    public readonly state: FileSystemState,
  ) {

  }

  get hasSearchViews(): boolean {
    return this.searchViews.length > 0;
  }

  toggleSearchTabsVisibility(): void {
    this.searchTabsVisible = !this.searchTabsVisible;
    if (!this.searchTabsVisible) {
      this.state.clearSearchCriteria();
      this.state.updateModelFromServer();
      return;
    }

    this.stateId = this.searchViews[0];
    this.state.updateModelFromServer();
  }

  get activeTabAsString(): string | number {
    return this.state.stateId?.toString() || '';
  }

  set activeTabAsString(value: string | number | null) {
    if (value == null || typeof value !== 'string') {
      return;
    }
    this.stateId = FileSystemViewId.fromString(value);
  }

  closeSearchView(id: FileSystemViewId) {
    this.fileSystemViewProvider.deleteView(id);

    const lastTabWasClosed = this.searchViews.length == 0;
    if (lastTabWasClosed) {
      this.state.clearSearchCriteria();
      this.state.updateModelFromServer();
      this.searchTabsVisible = false;
      return;
    }

    this.stateId = this.searchViews[this.searchViews.length - 1];
  }
  set stateId(value: FileSystemViewId) {
    this.state.stateId = value;
    this.currentSearchCriteria = value.searchCriteria ?? SearchCriteria.empty();
  }
  get availableViewModes(): FileSystemViewMode[] {
    return ['status', 'date', 'case'];
  }

  get currentViewModeLabel() {
    return this.getViewModeLabel(this.state.viewMode);
  };
  get showSearchResults(): boolean {
    return this.state.searchCriteria !== null;
  }

  get viewMode(): FileSystemViewMode {
    return this.state.viewMode;
  }

  ngOnInit(): void {
    this.subscription
      .add(this.state.model$
        .subscribe(model => this.model = model));
    this.subscription
      .add(this.state.onSelectedFile
        .subscribe(path => this.selectedFilePath = path))
    this.subscription.add(
      this.notification.notifications$.subscribe(message => {
        if (!message.path || !this.model) {
          return;
        }
        this.model.updateFileByPath(message.path, file => file.hasNotification = true);
      })
    );
    this.subscription.add(
      this.fileSystemViewProvider.onViewAdded.subscribe(viewId => {
        if (viewId.isSearchView) {
          this.searchViews.push(viewId);
          this.searchTabsVisible = true;
        }
      })
    );
    this.subscription.add(
      this.fileSystemViewProvider.onViewRemoved.subscribe(removedViewId => {
        if (removedViewId.isSearchView) {
          const index = this.searchViews.findIndex(id => id.equals(removedViewId));
          if (index !== -1) {
            this.searchViews.splice(index, 1);
          }
        }
      })
    );
    document.addEventListener('keydown', (event) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
        event.preventDefault();
        this.searchFormVisible = !this.searchFormVisible;
      }
    });
  }
  ngOnDestroy(): void {
    document.removeEventListener('keydown', () => {});
    this.subscription.unsubscribe();
  }
  getViewModeLabel(mode: FileSystemViewMode) {
    switch (mode) {
      case 'status': return 'Status';
      case 'date': return 'Date';
      case 'case': return 'Case';
    }
  }

  expandByKey(key: string | null | undefined) {
    const folderPath = this.model?.getPathOf(key ?? '');
    if (!folderPath) {
      return;
    }
    this.state.expand(folderPath);
  }

  getSearchResultsCount(): number {
    if (!this.model || !this.showSearchResults) {
      return 0;
    }
    return this.model.allFiles.length;
  }
}

