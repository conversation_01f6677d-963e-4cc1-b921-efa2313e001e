.file-system-container {
  @apply px-3 py-1; /* Reduced padding */
  height: calc(100vh - 3rem); /* Slightly less than full height to prevent scrolling */
  overflow: hidden; /* Prevent the whole page from scrolling */
  display: flex; /* Ensure flex layout */
  gap: 16px; /* Add consistent gap between elements */
  width: 100%; /* Ensure container takes full width */
  box-sizing: border-box; /* Include padding in width calculation */
}
@media (max-width: 768px) {
  .file-system-container {
    flex-direction: column;
  }
}
.file-system-container .pdf-section {
  @apply flex flex-col;
  flex: 2; /* Take 2/3 of the available space (2:1 ratio) */
  min-width: 0; /* Allow flex to shrink below content size if needed */
  height: calc(100vh - 4rem); /* Match the file-system height */
  width: calc(66.666% - 8px); /* Explicitly set width to 2/3 minus half the gap */
}
@media (max-width: 768px) {
  .file-system-container .pdf-section {
    width: 100%;
    flex: none;
    height: auto;
  }
}
.file-system-container .pdf-section .pdf-viewer-wrapper {
  @apply flex flex-col;
  width: 100%; /* Take full width of parent */
  height: 100%; /* Take full height of parent */
  display: flex;
  flex-direction: column;
}
.file-system-container .file-actions-above-pdf {
  width: 100%; /* Take full width of the pdf-section */
  flex: 0 0 auto; /* Don't grow or shrink, use auto height */
}

.file-system {
  @apply overflow-hidden flex flex-col;
  background: linear-gradient(to bottom, #EFF6FF, #DBEAFE);
  height: calc(100vh - 4rem);
  flex: 1; /* Take 1/3 of the available space (1:2 ratio with pdf-section) */
  width: calc(33.333% - 8px); /* Explicitly set width to 1/3 minus half the gap */
  min-width: 250px; /* Minimum width for usability */
  transition: all 0.3s ease;
  /* Handle toolbar overflow */
}
@media (max-width: 768px) {
  .file-system {
    width: 100%;
    flex: none;
    margin-bottom: 1rem;
  }
}
.file-system .toolbar {
  @apply flex-wrap;
  min-height: 40px; /* Reduced height */
}
.file-system .toolbar .view-mode-selector {
  @apply flex-shrink-0;
}
.file-system .toolbar button {
  @apply flex-shrink-0;
}
.file-system .loading-spinner {
  @apply inline-block rounded-full;
  border: 2px solid rgba(37, 99, 235, 0.2);
  border-top-color: rgba(37, 99, 235, 0.9);
  animation: spin 1s linear infinite;
}
.file-system .loading-spinner-large {
  @apply inline-block rounded-full h-12 w-12;
  border: 3px solid rgba(37, 99, 235, 0.2);
  border-top-color: rgba(37, 99, 235, 0.9);
  animation: spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}
.file-system .tree-loading-overlay {
  @apply absolute inset-0 bg-white/90 backdrop-blur-sm z-10 flex items-center justify-center;
  animation: fadeIn 0.3s ease-in-out;
  border-top: 1px solid rgba(37, 99, 235, 0.1);
}
.file-system .loading-content {
  @apply flex flex-col items-center justify-center p-6 rounded-xl bg-white shadow-lg border border-blue-100;
  position: relative;
  overflow: hidden;
  max-width: 300px;
}
.file-system .loading-content::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-100%);
  background-image: linear-gradient(90deg, rgba(255, 255, 255, 0) 0, rgba(255, 255, 255, 0.2) 20%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0));
  animation: shimmer 2s infinite;
}
@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}
.file-system .loading-text {
  @apply mt-4 text-lg font-medium text-blue-700;
  animation: pulse 2s infinite;
}
.file-system .loading-progress {
  @apply mt-3 w-48 h-2 bg-blue-100 rounded-full overflow-hidden;
}
.file-system .loading-progress-bar {
  @apply h-full bg-blue-500 rounded-full;
  animation: progressBar 2s ease-in-out infinite;
  width: 0;
}
.file-system .loading-progress-bar.search-progress {
  @apply bg-amber-500;
  animation: searchProgressBar 1.5s ease-in-out infinite;
}
@keyframes searchProgressBar {
  0% {
    width: 0;
  }
  50% {
    width: 85%;
  }
  90% {
    width: 95%;
  }
  100% {
    width: 95%;
  }
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes progressBar {
  0% {
    width: 0;
  }
  50% {
    width: 70%;
  }
  80% {
    width: 90%;
  }
  100% {
    width: 100%;
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.file-system .view-mode-selector .view-mode-btn {
  transition: all 0.2s ease-in-out;
}
.file-system .view-mode-selector .view-mode-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.3);
}
.file-system .search-toggle-btn {
  @apply flex items-center justify-center w-10 h-10 rounded-full bg-blue-600 text-white shadow-sm relative overflow-hidden;
  transition: all 0.2s ease;
  border: none;
}
.file-system .search-toggle-btn:hover {
  @apply bg-blue-500 shadow-md;
}
.file-system .search-toggle-btn:active {
  @apply shadow-inner bg-blue-700 transform scale-95;
}
.file-system .search-toggle-btn.active {
  @apply bg-blue-700 shadow-inner;
  transform: translateY(1px);
}
.file-system .search-toggle-btn i {
  @apply text-white text-lg;
}
.file-system .search-form-container {
  @apply overflow-hidden bg-gradient-to-br from-blue-50 to-white;
}
.file-system .search-form-content {
  @apply p-6 pb-8;
}
.file-system .search-form-header {
  @apply flex items-center justify-between mb-6;
}
.file-system .search-form-title {
  @apply text-xl font-bold text-blue-700 flex items-center;
}
.file-system .search-form-title i {
  @apply text-blue-500;
}
.file-system .search-form-close {
  @apply flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200;
  transition: all 0.2s ease;
  border: none;
}
.file-system .search-form-close:hover {
  @apply transform rotate-90;
}
.file-system .search-form-grid {
  @apply grid gap-6;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.file-system .search-form-group {
  @apply flex flex-col;
}
.file-system .search-form-label {
  @apply flex items-center text-sm font-medium text-blue-700 mb-2;
}
.file-system .search-form-label i {
  @apply text-blue-500;
}
.file-system .search-input-container {
  @apply relative;
}
.file-system .search-input-container:focus-within .search-input-focus-indicator {
  @apply scale-x-100;
}
.file-system .search-input-container.calendar-container {
  @apply w-full;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar {
  @apply w-full;
  transition: transform 0.2s ease;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar:hover {
  transform: translateY(-1px);
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-inputtext {
  @apply w-full p-3 rounded-lg border border-blue-200 bg-white shadow-sm text-blue-800;
  transition: all 0.2s ease;
  font-family: inherit;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-inputtext:focus {
  @apply border-blue-300 shadow outline-none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-inputtext:hover:not(:disabled) {
  @apply border-blue-300;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker-trigger {
  @apply bg-blue-600 border-blue-600 rounded-r-lg;
  transition: all 0.2s ease;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker-trigger:hover {
  @apply bg-blue-500 border-blue-500;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker-trigger:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker {
  @apply rounded-xl shadow-lg border border-blue-100;
  animation: calendarFadeIn 0.2s ease-out;
  z-index: 1000;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-header {
  @apply bg-blue-50 border-blue-100;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month, .file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year {
  @apply text-blue-800 font-medium;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month:hover, .file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year:hover {
  @apply text-blue-600;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-header .p-datepicker-prev, .file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-header .p-datepicker-next {
  @apply text-blue-600;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-header .p-datepicker-prev:hover, .file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-header .p-datepicker-next:hover {
  @apply bg-blue-100 text-blue-800;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar th {
  @apply text-blue-600;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar td .p-highlight {
  @apply bg-blue-600 text-white;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar td .p-ripple:hover:not(.p-disabled):not(.p-highlight) {
  @apply bg-blue-100 text-blue-800;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar td .p-disabled {
  @apply opacity-50;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-calendar td .p-datepicker-today > span {
  @apply border-blue-400 text-blue-600;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-buttonbar {
  @apply border-t border-blue-100 bg-blue-50;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-buttonbar button {
  @apply text-blue-600 font-medium;
}
.file-system .search-input-container.calendar-container ::ng-deep .p-calendar .p-datepicker .p-datepicker-buttonbar button:hover {
  @apply bg-blue-100 text-blue-800;
}
.file-system .search-form-input {
  @apply w-full p-3 rounded-lg border border-blue-200 bg-white shadow-sm;
  transition: all 0.2s ease;
}
.file-system .search-form-input:focus {
  @apply border-blue-300 shadow outline-none;
}
.file-system .search-input-focus-indicator {
  @apply absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 rounded-full transform scale-x-0;
  transition: transform 0.3s ease;
  transform-origin: left;
}
.file-system ::ng-deep body .p-datepicker {
  z-index: 1100 !important;
  @apply rounded-xl shadow-lg;
  animation: calendarFadeIn 0.2s ease-out;
}
@keyframes calendarFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.file-system .search-form-actions {
  @apply flex items-center gap-3 mt-2;
  grid-column: 1/-1;
}
.file-system .search-submit-btn {
  @apply relative flex items-center px-5 py-2.5 rounded-lg bg-blue-600 text-white font-medium shadow-sm;
  transition: all 0.2s ease;
  border: none;
  min-width: 120px;
}
.file-system .search-submit-btn:hover:not(:disabled) {
  @apply bg-blue-500 shadow-md;
}
.file-system .search-submit-btn:active:not(:disabled) {
  @apply shadow-inner bg-blue-700 transform scale-95;
}
.file-system .search-submit-btn:disabled {
  @apply opacity-70 cursor-not-allowed bg-blue-300;
}
.file-system .search-submit-btn .search-spinner {
  @apply inline-block h-4 w-4 rounded-full;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}
.file-system .search-cancel-btn {
  @apply flex items-center px-5 py-2.5 rounded-lg bg-white border border-blue-200 text-blue-700 font-medium;
  transition: all 0.2s ease;
}
.file-system .search-cancel-btn:hover:not(:disabled) {
  @apply bg-blue-50 border-blue-300 shadow-sm;
}
.file-system .search-cancel-btn:active:not(:disabled) {
  @apply bg-blue-100 transform scale-95;
}
.file-system .search-cancel-btn:disabled {
  @apply opacity-50 cursor-not-allowed bg-gray-50 text-gray-400 border-gray-200;
}
.file-system .view-mode-indicator {
  @apply p-2 border-b border-blue-200 bg-blue-50; /* Reduced padding */
  transition: all 0.3s ease;
}
.file-system .view-mode-indicator.search-results-active {
  @apply bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200;
}
.file-system .view-mode-indicator.with-tabs {
  @apply top-[2.5rem];
}
.file-system .view-mode-indicator.with-hidden-tabs {
  @apply top-0;
}
.file-system .view-mode-indicator .view-mode-content {
  @apply flex items-center justify-between;
}
.file-system .view-mode-indicator .view-mode-info {
  @apply flex items-center;
}
.file-system .view-mode-indicator .view-mode-icon {
  @apply flex items-center justify-center w-10 h-10 rounded-full mr-3;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.5));
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.7);
}
.file-system .view-mode-indicator .view-mode-icon i {
  @apply text-blue-600 text-lg;
}
.search-results-active .file-system .view-mode-indicator .view-mode-icon i {
  @apply text-amber-600;
}
.file-system .view-mode-indicator .view-mode-text {
  @apply flex flex-col;
}
.file-system .view-mode-indicator .view-mode-label {
  @apply text-sm font-medium text-blue-700;
}
.file-system .view-mode-indicator .view-mode-label strong {
  @apply text-blue-800;
}
.search-results-active .file-system .view-mode-indicator .view-mode-label {
  @apply text-amber-700;
}
.search-results-active .file-system .view-mode-indicator .view-mode-label strong {
  @apply text-amber-800;
}
.file-system .view-mode-indicator .view-mode-description {
  @apply text-xs text-blue-600;
}
.file-system .view-mode-indicator .view-mode-description.search-results-text {
  @apply text-amber-600 font-medium;
}
.file-system .view-mode-indicator .search-results-count {
  @apply inline-flex items-center justify-center px-2 py-0.5 rounded-full bg-amber-200 text-amber-800 font-bold;
  animation: pulse 2s infinite;
}
.file-system .view-mode-indicator .clear-search-btn {
  @apply flex items-center px-3 py-1.5 rounded-full bg-gradient-to-r from-amber-500 to-amber-400 text-white font-medium shadow-sm;
  transition: all 0.2s ease;
  border: none;
}
.file-system .view-mode-indicator .clear-search-btn:hover {
  @apply from-amber-400 to-amber-300 shadow transform -translate-y-0.5;
}
.file-system .view-mode-indicator .clear-search-btn:active {
  @apply shadow-inner transform scale-95;
}
.file-system .crossed-text {
  text-decoration: line-through;
  text-decoration-thickness: 2px;
  text-decoration-color: rgba(37, 99, 235, 0.5);
  color: #60a5fa; /* blue-400 equivalent */
}
.file-system .red-text {
  color: #ef4444; /* red-500 equivalent */
  font-weight: 500; /* medium equivalent */
}
.file-system .crossed-text.red-text {
  color: #f87171; /* red-400 equivalent */
  text-decoration-color: rgba(239, 68, 68, 0.8);
}
.file-system .tree-container {
  @apply flex-1 overflow-y-auto;
  height: calc(100% - 45px); /* Adjusted for reduced toolbar height */
}
.file-system .file-tree {
  user-select: none;
  height: 100%; /* Fill the container height */
  overflow-x: hidden;
}
.file-system .file-tree .p-treenode-content {
  @apply rounded-lg px-2 py-1; /* Reduced padding */
  @apply hover:bg-blue-100/70 border border-transparent hover:border-blue-200;
  transition: all 0.2s ease;
  display: flex;
  flex-wrap: nowrap;
  min-width: 0;
}
.file-system .file-tree .p-treenode-content:hover {
  @apply shadow-sm transform -translate-y-px;
}
.file-system .file-tree .p-treenode-content:hover .p-treenode-label {
  @apply text-blue-700;
}
.file-system .file-tree .p-treenode-content .p-treenode-label {
  @apply truncate;
  max-width: 100%;
  flex: 1;
  min-width: 0;
}
.file-system .file-tree .p-treenode-content small {
  @apply text-xs font-normal bg-blue-100/50 px-1.5 py-0.5 rounded-full;
  transition: all 0.2s ease;
  white-space: nowrap;
}
.file-system .file-tree .p-treenode-content small[class*=text-blue] {
  @apply bg-blue-100/80;
}
.file-system .file-tree .p-treenode-content small[class*=text-blue-600] {
  @apply bg-blue-100/90;
}
.search-results-active .file-system .file-tree .p-treenode-content {
  @apply hover:bg-amber-100/70 hover:border-amber-200;
}
.search-results-active .file-system .file-tree .p-treenode-content:hover {
  @apply shadow-md;
  animation: searchItemHover 0.3s ease;
}
.search-results-active .file-system .file-tree [ptemplate=pdf] .p-treenode-content {
  @apply bg-white/80 border-amber-100;
  position: relative;
  overflow: hidden;
}
.search-results-active .file-system .file-tree [ptemplate=pdf] .p-treenode-content::before {
  content: "";
  @apply absolute inset-0 bg-gradient-to-r from-amber-50/30 to-transparent;
  transform: translateX(-100%);
  animation: searchHighlight 2s ease-in-out;
  animation-delay: calc(var(--tree-node-index, 0) * 0.1s);
}
.search-results-active .file-system .file-tree [ptemplate=pdf] .p-treenode-content:hover::before {
  animation: none;
}
@keyframes searchHighlight {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}
@keyframes searchItemHover {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
  100% {
    transform: translateY(-1px);
  }
}
.file-system .pi-folder {
  @apply text-amber-500;
}
.file-system .pi-file-edit {
  @apply text-blue-600;
}
.file-system .pi-image {
  @apply text-blue-400;
}
.file-system .pi-file-pdf {
  @apply text-red-600 font-bold;
}
.file-system .pi-file {
  @apply text-blue-600;
}
.file-system .file-metadata .icon {
  @apply text-lg;
}
.file-system .p-treenode-toggler {
  @apply hover:bg-blue-200 rounded-full;
  transition: all 0.2s ease;
}
.file-system .p-treenode-toggler.pi-chevron-down {
  @apply rotate-90;
}
.file-system .p-treenode-toggler:hover {
  @apply transform scale-110;
}
.file-system .btn-action {
  @apply hover:bg-blue-200 text-blue-800 rounded-full w-9 h-9 flex items-center justify-center;
  transition: all 0.2s ease;
}
.file-system .btn-action:hover {
  @apply transform scale-105 shadow-sm;
}
.file-system .btn-action:disabled {
  @apply opacity-50 cursor-not-allowed;
}
.file-system .btn-action i {
  @apply text-lg;
}
.file-system .btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white rounded-full px-3 py-1.5;
  transition: all 0.2s ease;
}
.file-system .btn-primary:hover:not(:disabled) {
  @apply transform scale-105 shadow-md;
}
.file-system .btn-primary:disabled {
  @apply opacity-60 cursor-not-allowed bg-gray-400;
}
.file-system .btn-danger {
  @apply bg-red-500 hover:bg-red-600 text-white rounded-full px-3 py-1.5;
  transition: all 0.2s ease;
}
.file-system .btn-danger:hover:not(:disabled) {
  @apply transform scale-105 shadow-md;
}
.file-system .btn-danger:disabled {
  @apply opacity-60 cursor-not-allowed bg-gray-400;
}
.file-system .btn-secondary {
  @apply bg-blue-200 hover:bg-blue-300 text-blue-700 rounded-full px-3 py-1.5;
  transition: all 0.2s ease;
}
.file-system .btn-secondary:hover:not(:disabled) {
  @apply transform scale-105 shadow-md;
}
.file-system .btn-secondary:disabled {
  @apply opacity-60 cursor-not-allowed;
}
.file-system .load-more-container {
  @apply flex items-center justify-between w-52 whitespace-nowrap px-1.5 py-0.5 border border-blue-200 bg-white/80 text-xs rounded-lg shadow-sm; /* Reduced size and padding */
  backdrop-filter: blur(4px);
}
.file-system .load-more-btn {
  @apply inline-flex items-center cursor-pointer px-2 py-0.5 font-medium text-blue-700 hover:text-blue-500 hover:bg-blue-100/70 rounded-md; /* Reduced padding */
  transition: all 0.15s ease;
}
.file-system .load-more-btn:hover {
  @apply transform scale-105 shadow-sm;
}
.file-system .load-more-btn:first-child {
  @apply bg-blue-50/80;
}
.file-system .load-more-btn:last-child {
  @apply bg-blue-100/80;
}
.file-system .selected-files-section {
  @apply border-t border-blue-300 bg-gradient-to-b from-blue-100 to-blue-50 flex flex-col h-64;
  box-shadow: 0 -4px 6px -1px rgba(37, 99, 235, 0.05);
}
.file-system .selected-files-header {
  @apply p-4 border-b border-blue-200 sticky top-0 z-10 bg-blue-100 shadow-sm flex items-center justify-between;
  background: linear-gradient(to right, #DBEAFE, #EFF6FF);
}
.file-system .action-buttons {
  @apply flex gap-1.5;
}
.file-system .selected-files-content {
  @apply p-4 pt-2 overflow-y-auto flex-1 flex flex-col gap-2;
}
.file-system .file-item {
  @apply flex items-center p-3 rounded-xl bg-white text-blue-800 shadow-sm border border-blue-200 hover:bg-blue-50;
  transition: all 0.2s ease;
}
.file-system .file-item:hover {
  @apply shadow-md border-blue-300 transform -translate-y-px;
}
.file-system .file-item::after {
  content: attr(data-filetype);
  @apply ml-2 text-xs font-medium px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-700;
}
.file-system .no-files-message {
  @apply text-sm text-blue-600 p-3 bg-white rounded-xl shadow-sm border border-blue-200;
}
.file-system ::ng-deep .action-menu-modern {
  @apply rounded-xl shadow-xl border border-gray-100 min-w-[220px];
}
.file-system ::ng-deep .action-menu-modern .p-menu-list {
  @apply p-1.5;
}
.file-system ::ng-deep .action-menu-modern .p-menuitem {
  @apply rounded-lg overflow-hidden my-0.5;
}
.file-system ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link {
  @apply rounded-lg px-3 py-2;
}
.file-system ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link:hover {
  @apply bg-gray-50;
}
.file-system ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link .p-menuitem-icon {
  @apply text-gray-500 mr-3;
}
.file-system ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link .p-menuitem-text {
  @apply text-sm text-gray-700;
}
.file-system ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link:hover .p-menuitem-icon {
  @apply text-blue-600;
}
.file-system ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link.p-disabled {
  @apply opacity-50;
}
.file-system .empty-state {
  @apply flex items-center justify-center h-full;
}
.file-system .empty-state-content {
  @apply flex flex-col items-center justify-center p-8 rounded-2xl bg-white/80 shadow-lg border border-blue-100;
  animation: fadeInUp 0.5s ease-out;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.file-system .tree-container, .file-system .selected-files-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.4) transparent;
}
.file-system .tree-container::-webkit-scrollbar, .file-system .selected-files-content::-webkit-scrollbar {
  @apply w-2;
}
.file-system .tree-container::-webkit-scrollbar-track, .file-system .selected-files-content::-webkit-scrollbar-track {
  @apply bg-transparent;
}
.file-system .tree-container::-webkit-scrollbar-thumb, .file-system .selected-files-content::-webkit-scrollbar-thumb {
  @apply bg-blue-200 rounded-full;
}
.file-system .tree-container::-webkit-scrollbar-thumb:hover, .file-system .selected-files-content::-webkit-scrollbar-thumb:hover {
  @apply bg-blue-300;
}
.file-system .file-status {
  @apply inline-flex items-center justify-center h-3 w-3 rounded-full;
}
.file-system .file-status.status-normal {
  @apply bg-green-400;
}
.file-system .file-status.status-warning {
  @apply bg-yellow-400;
}
.file-system .file-status.status-error {
  @apply bg-red-400;
}
.file-system .notification-indicator {
  @apply inline-flex items-center justify-center h-5 w-5 rounded-full bg-yellow-500 text-white font-bold text-xs;
  animation: pulse 1.5s infinite;
}
.file-system .search-result-indicator {
  @apply absolute right-0 top-0 bottom-0 w-1 bg-amber-400 rounded-r-lg;
  animation: pulseGlow 2s infinite;
}
@keyframes pulseGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(251, 191, 36, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(251, 191, 36, 0);
  }
}
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
.file-system .file-metadata {
  @apply text-xs text-blue-600 flex items-center gap-1.5;
}
.file-system .file-metadata .metadata-item {
  @apply flex items-center;
}
.file-system .file-metadata .metadata-item .icon {
  @apply mr-1 text-blue-400;
}

.pdf-viewer-container {
  width: 66.667%; /* Changed from 49% to 2/3 */
  transition: width 0.3s ease;
  height: calc(100vh - 4rem);
  flex-grow: 1; /* Allow growing to fill available space */
  min-width: 400px; /* Ensure minimum width for usability */
}
@media (max-width: 768px) {
  .pdf-viewer-container {
    display: none;
  }
}

/* Search results toggle button */
.search-results-toggle-btn {
  @apply flex items-center justify-center w-9 h-9 rounded-full bg-white shadow-sm border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors relative;
}
.search-results-toggle-btn.active {
  @apply bg-blue-600 text-white border-blue-700 hover:bg-blue-700;
}
.search-results-toggle-btn i {
  @apply text-lg;
}

.search-count-indicator {
  @apply absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 rounded-full bg-amber-500 text-white text-xs font-bold;
}

/* Search tabs wrapper with transitions */
.search-tabs-wrapper {
  @apply sticky top-0 z-20 transition-all duration-300 ease-in-out overflow-hidden;
  margin: 0;
  padding: 0;
}
.search-tabs-wrapper.visible {
  max-height: 2.5rem; /* 40px */
  opacity: 1;
}
.search-tabs-wrapper.hidden {
  max-height: 0;
  opacity: 0;
}
.search-tabs-wrapper.no-tabs {
  display: none;
}

/* Tabs styling */
.search-tabs-container {
  @apply sticky top-0 z-20 bg-white border-b border-blue-200;
  margin: 0;
  padding: 0;
}
.search-tabs-container ::ng-deep .p-tablist {
  @apply bg-white border-b border-blue-200 shadow-sm;
  margin: 0;
  padding: 0;
}
.search-tabs-container ::ng-deep .p-tab {
  @apply py-1 px-4 text-sm font-medium text-blue-700 hover:text-blue-900 transition-colors;
  height: 2.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.search-tabs-container ::ng-deep .p-tab:hover {
  @apply bg-blue-50;
}
.search-tabs-container ::ng-deep .p-tab.p-highlight {
  @apply text-blue-900 bg-blue-100/50 border-b-2 border-blue-500;
}
.search-tabs-container ::ng-deep .p-tab.p-highlight .viewmode-badge {
  opacity: 1;
}
.search-tabs-container ::ng-deep .p-tablist-active-bar {
  @apply bg-blue-500;
}

.tab-close-btn {
  @apply flex items-center justify-center w-5 h-5 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-800;
  transition: all 0.2s ease;
  z-index: 2 !important;
}
.tab-close-btn:hover {
  @apply transform rotate-90;
}
.tab-close-btn i {
  @apply text-xs;
}

/* Hidden tabs indicator */
.hidden-tabs-indicator {
  @apply inline-flex items-center ml-3 py-0.5 px-2 rounded-full bg-amber-100 text-amber-800 text-xs font-medium;
}

/* View mode with hidden tabs */
.view-mode-indicator.with-hidden-tabs {
  @apply bg-gradient-to-r from-blue-50 to-amber-50 border-amber-200;
}

/* View mode actions container */
.view-mode-actions {
  @apply flex items-center gap-2;
}

/* Show tabs button */
.show-tabs-btn {
  @apply flex items-center py-1 px-2 rounded bg-amber-100 text-amber-800 text-sm font-medium hover:bg-amber-200 transition-colors;
}

.show-saved-searches-btn {
  @apply inline-flex items-center py-1 px-2.5 rounded-full bg-amber-100 text-amber-800 text-xs font-medium;
  transition: all 0.2s ease;
}
.show-saved-searches-btn:hover {
  @apply bg-amber-200 shadow-sm;
}
.show-saved-searches-btn:active {
  @apply bg-amber-300 transform scale-95;
}
.show-saved-searches-btn:focus {
  @apply outline-none ring-2 ring-amber-300;
}

.p-tab > .p-button-text {
  z-index: 5;
}

.viewmode-badge {
  @apply inline-flex items-center justify-center px-1.5 py-0.5 ml-1.5 text-xs font-medium rounded;
  background-color: #3b82f6;
  color: white;
  transition: transform 0.15s ease;
  position: relative;
  text-align: center;
  font-size: 0.7rem;
  line-height: 1;
}
.viewmode-badge:hover {
  transform: translateY(-1px);
}
.viewmode-badge.viewmode-status {
  background-color: #3b82f6;
}
.viewmode-badge.viewmode-date {
  background-color: #8b5cf6;
}
.viewmode-badge.viewmode-case {
  background-color: #10b981;
}

/*# sourceMappingURL=file-system.component.css.map */
