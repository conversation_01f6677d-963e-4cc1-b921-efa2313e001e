.file-system-container {
  @apply px-3 py-1; /* Reduced padding */
  height: calc(100vh - 3rem); /* Slightly less than full height to prevent scrolling */
  overflow: hidden; /* Prevent the whole page from scrolling */
  display: flex; /* Ensure flex layout */
  gap: 16px; /* Add consistent gap between elements */
  width: 100%; /* Ensure container takes full width */
  box-sizing: border-box; /* Include padding in width calculation */

  @media (max-width: 768px) {
    flex-direction: column;
  }

  .pdf-section {
    @apply flex flex-col;
    flex: 2; /* Take 2/3 of the available space (2:1 ratio) */
    min-width: 0; /* Allow flex to shrink below content size if needed */
    height: calc(100vh - 4rem); /* Match the file-system height */
    width: calc(66.666% - 8px); /* Explicitly set width to 2/3 minus half the gap */

    @media (max-width: 768px) {
      width: 100%;
      flex: none;
      height: auto;
    }

    .pdf-viewer-wrapper {
      @apply flex flex-col;
      width: 100%; /* Take full width of parent */
      height: 100%; /* Take full height of parent */
      display: flex;
      flex-direction: column;
    }
  }

  .file-actions-above-pdf {
    width: 100%; /* Take full width of the pdf-section */
    flex: 0 0 auto; /* Don't grow or shrink, use auto height */
  }
}

.file-system {
  @apply overflow-hidden flex flex-col;
  background: linear-gradient(to bottom, #EFF6FF, #DBEAFE);
  height: calc(100vh - 4rem);
  flex: 1; /* Take 1/3 of the available space (1:2 ratio with pdf-section) */
  width: calc(33.333% - 8px); /* Explicitly set width to 1/3 minus half the gap */
  min-width: 250px; /* Minimum width for usability */
  transition: all 0.3s ease;

  @media (max-width: 768px) {
    width: 100%;
    flex: none;
    margin-bottom: 1rem;
  }

  /* Handle toolbar overflow */
  .toolbar {
    @apply flex-wrap;
    min-height: 40px; /* Reduced height */

    .view-mode-selector {
      @apply flex-shrink-0;
    }

    button {
      @apply flex-shrink-0;
    }
  }

  // Loading spinner animation
  .loading-spinner {
    @apply inline-block rounded-full;
    border: 2px solid rgba(37, 99, 235, 0.2);
    border-top-color: rgba(37, 99, 235, 0.9);
    animation: spin 1s linear infinite;
  }

  .loading-spinner-large {
    @apply inline-block rounded-full h-12 w-12;
    border: 3px solid rgba(37, 99, 235, 0.2);
    border-top-color: rgba(37, 99, 235, 0.9);
    animation: spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  }

  // Loading overlay for tree view only
  .tree-loading-overlay {
    @apply absolute inset-0 bg-white/90 backdrop-blur-sm z-10 flex items-center justify-center;
    animation: fadeIn 0.3s ease-in-out;
    border-top: 1px solid rgba(37, 99, 235, 0.1);
  }

  .loading-content {
    @apply flex flex-col items-center justify-center p-6 rounded-xl bg-white shadow-lg border border-blue-100;
    position: relative;
    overflow: hidden;
    max-width: 300px;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      transform: translateX(-100%);
      background-image: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0,
        rgba(255, 255, 255, 0.2) 20%,
        rgba(255, 255, 255, 0.5) 60%,
        rgba(255, 255, 255, 0)
      );
      animation: shimmer 2s infinite;
    }
  }

  @keyframes shimmer {
    100% {
      transform: translateX(100%);
    }
  }

  .loading-text {
    @apply mt-4 text-lg font-medium text-blue-700;
    animation: pulse 2s infinite;
  }

  .loading-progress {
    @apply mt-3 w-48 h-2 bg-blue-100 rounded-full overflow-hidden;
  }

  .loading-progress-bar {
    @apply h-full bg-blue-500 rounded-full;
    animation: progressBar 2s ease-in-out infinite;
    width: 0;

    &.search-progress {
      @apply bg-amber-500;
      animation: searchProgressBar 1.5s ease-in-out infinite;
    }
  }

  @keyframes searchProgressBar {
    0% { width: 0; }
    50% { width: 85%; }
    90% { width: 95%; }
    100% { width: 95%; }
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes progressBar {
    0% { width: 0; }
    50% { width: 70%; }
    80% { width: 90%; }
    100% { width: 100%; }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // View mode styles
  .view-mode-selector {
    .view-mode-btn {
      transition: all 0.2s ease-in-out;

      &:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.3);
      }
    }
  }

  // Search button styles
  .search-toggle-btn {
    @apply flex items-center justify-center w-10 h-10 rounded-full bg-blue-600 text-white shadow-sm relative overflow-hidden;
    transition: all 0.2s ease;
    border: none;

    &:hover {
      @apply bg-blue-500 shadow-md;
    }

    &:active {
      @apply shadow-inner bg-blue-700 transform scale-95;
    }

    &.active {
      @apply bg-blue-700 shadow-inner;
      transform: translateY(1px);
    }

    i {
      @apply text-white text-lg;
    }
  }

  // Search form styles
  .search-form-container {
    @apply overflow-hidden bg-gradient-to-br from-blue-50 to-white;
    // Remove max-height: 0 as it will be controlled by the animation
  }

  .search-form-content {
    @apply p-6 pb-8;
  }

  .search-form-header {
    @apply flex items-center justify-between mb-6;
  }

  .search-form-title {
    @apply text-xl font-bold text-blue-700 flex items-center;

    i {
      @apply text-blue-500;
    }
  }

  .search-form-close {
    @apply flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200;
    transition: all 0.2s ease;
    border: none;

    &:hover {
      @apply transform rotate-90;
    }
  }

  .search-form-grid {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .search-form-group {
    @apply flex flex-col;
  }

  .search-form-label {
    @apply flex items-center text-sm font-medium text-blue-700 mb-2;

    i {
      @apply text-blue-500;
    }
  }

  .search-input-container {
    @apply relative;

    &:focus-within .search-input-focus-indicator {
      @apply scale-x-100;
    }

    // Calendar container styling
    &.calendar-container {
      @apply w-full;

      ::ng-deep .p-calendar {
        @apply w-full;
        transition: transform 0.2s ease;

        &:hover {
          transform: translateY(-1px);
        }

        .p-inputtext {
          @apply w-full p-3 rounded-lg border border-blue-200 bg-white shadow-sm text-blue-800;
          transition: all 0.2s ease;
          font-family: inherit;

          &:focus {
            @apply border-blue-300 shadow outline-none;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
          }

          &:hover:not(:disabled) {
            @apply border-blue-300;
          }
        }

        // Calendar button styling
        .p-datepicker-trigger {
          @apply bg-blue-600 border-blue-600 rounded-r-lg;
          transition: all 0.2s ease;

          &:hover {
            @apply bg-blue-500 border-blue-500;
          }

          &:focus {
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
          }
        }

        // Calendar panel styling
        .p-datepicker {
          @apply rounded-xl shadow-lg border border-blue-100;
          animation: calendarFadeIn 0.2s ease-out;
          z-index: 1000; // Ensure calendar appears above other elements

          .p-datepicker-header {
            @apply bg-blue-50 border-blue-100;

            .p-datepicker-title {
              .p-datepicker-month, .p-datepicker-year {
                @apply text-blue-800 font-medium;

                &:hover {
                  @apply text-blue-600;
                }
              }
            }

            .p-datepicker-prev, .p-datepicker-next {
              @apply text-blue-600;

              &:hover {
                @apply bg-blue-100 text-blue-800;
              }
            }
          }

          .p-datepicker-calendar {
            th {
              @apply text-blue-600;
            }

            td {
              .p-highlight {
                @apply bg-blue-600 text-white;
              }

              .p-ripple:hover:not(.p-disabled):not(.p-highlight) {
                @apply bg-blue-100 text-blue-800;
              }

              .p-disabled {
                @apply opacity-50;
              }

              .p-datepicker-today > span {
                @apply border-blue-400 text-blue-600;
              }
            }
          }

          .p-datepicker-buttonbar {
            @apply border-t border-blue-100 bg-blue-50;

            button {
              @apply text-blue-600 font-medium;

              &:hover {
                @apply bg-blue-100 text-blue-800;
              }
            }
          }
        }
      }
    }
  }

  .search-form-input {
    @apply w-full p-3 rounded-lg border border-blue-200 bg-white shadow-sm;
    transition: all 0.2s ease;

    &:focus {
      @apply border-blue-300 shadow outline-none;
    }
  }

  .search-input-focus-indicator {
    @apply absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 rounded-full transform scale-x-0;
    transition: transform 0.3s ease;
    transform-origin: left;
  }

  // Calendar panel styling for body-appended calendar
  ::ng-deep body .p-datepicker {
    z-index: 1100 !important; // Ensure calendar appears above all elements
    @apply rounded-xl shadow-lg;
    animation: calendarFadeIn 0.2s ease-out;
  }

  // Calendar animation
  @keyframes calendarFadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .search-form-actions {
    @apply flex items-center gap-3 mt-2;
    grid-column: 1 / -1;
  }

  .search-submit-btn {
    @apply relative flex items-center px-5 py-2.5 rounded-lg bg-blue-600 text-white font-medium shadow-sm;
    transition: all 0.2s ease;
    border: none;
    min-width: 120px;

    &:hover:not(:disabled) {
      @apply bg-blue-500 shadow-md;
    }

    &:active:not(:disabled) {
      @apply shadow-inner bg-blue-700 transform scale-95;
    }

    &:disabled {
      @apply opacity-70 cursor-not-allowed bg-blue-300;
    }

    .search-spinner {
      @apply inline-block h-4 w-4 rounded-full;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top-color: white;
      animation: spin 0.8s linear infinite;
    }
  }

  .search-cancel-btn {
    @apply flex items-center px-5 py-2.5 rounded-lg bg-white border border-blue-200 text-blue-700 font-medium;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      @apply bg-blue-50 border-blue-300 shadow-sm;
    }

    &:active:not(:disabled) {
      @apply bg-blue-100 transform scale-95;
    }

    &:disabled {
      @apply opacity-50 cursor-not-allowed bg-gray-50 text-gray-400 border-gray-200;
    }
  }

  // View mode indicator
  .view-mode-indicator {
    @apply p-2 border-b border-blue-200 bg-blue-50; /* Reduced padding */
    transition: all 0.3s ease;

    &.search-results-active {
      @apply bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200;
    }

    &.with-tabs {
      @apply top-[2.5rem];
    }

    &.with-hidden-tabs {
      @apply top-0;
    }

    .view-mode-content {
      @apply flex items-center justify-between;
    }

    .view-mode-info {
      @apply flex items-center;
    }

    .view-mode-icon {
      @apply flex items-center justify-center w-10 h-10 rounded-full mr-3;
      background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(255,255,255,0.5));
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      backdrop-filter: blur(5px);
      border: 1px solid rgba(255,255,255,0.7);

      i {
        @apply text-blue-600 text-lg;
      }

      .search-results-active & i {
        @apply text-amber-600;
      }
    }

    .view-mode-text {
      @apply flex flex-col;
    }

    .view-mode-label {
      @apply text-sm font-medium text-blue-700;

      strong {
        @apply text-blue-800;
      }
    }

    .search-results-active & .view-mode-label {
      @apply text-amber-700;

      strong {
        @apply text-amber-800;
      }
    }

    .view-mode-description {
      @apply text-xs text-blue-600;

      &.search-results-text {
        @apply text-amber-600 font-medium;
      }
    }

    .search-results-count {
      @apply inline-flex items-center justify-center px-2 py-0.5 rounded-full bg-amber-200 text-amber-800 font-bold;
      animation: pulse 2s infinite;
    }

    .clear-search-btn {
      @apply flex items-center px-3 py-1.5 rounded-full bg-gradient-to-r from-amber-500 to-amber-400 text-white font-medium shadow-sm;
      transition: all 0.2s ease;
      border: none;

      &:hover {
        @apply from-amber-400 to-amber-300 shadow transform -translate-y-0.5;
      }

      &:active {
        @apply shadow-inner transform scale-95;
      }
    }
  }

  // Text styles
  .crossed-text {
    text-decoration: line-through;
    text-decoration-thickness: 2px;
    text-decoration-color: rgba(37, 99, 235, 0.5);
    color: #60a5fa; /* blue-400 equivalent */
  }

  .red-text {
    color: #ef4444; /* red-500 equivalent */
    font-weight: 500; /* medium equivalent */
  }

  .crossed-text.red-text {
    color: #f87171; /* red-400 equivalent */
    text-decoration-color: rgba(239, 68, 68, 0.8);
  }

  // Container styles
  .tree-container {
    @apply flex-1 overflow-y-auto;
    height: calc(100% - 45px); /* Adjusted for reduced toolbar height */
  }

  // Tree component styling
  .file-tree {
    user-select: none;
    height: 100%; /* Fill the container height */
    overflow-x: hidden;

    .p-treenode-content {
      @apply rounded-lg px-2 py-1; /* Reduced padding */
      @apply hover:bg-blue-100/70 border border-transparent hover:border-blue-200;
      transition: all 0.2s ease;
      display: flex;
      flex-wrap: nowrap;
      min-width: 0;

      &:hover {
        @apply shadow-sm transform -translate-y-px;
      }

      &:hover .p-treenode-label {
        @apply text-blue-700;
      }

      .p-treenode-label {
        @apply truncate;
        max-width: 100%;
        flex: 1;
        min-width: 0;
      }

      small {
        @apply text-xs font-normal bg-blue-100/50 px-1.5 py-0.5 rounded-full;
        transition: all 0.2s ease;
        white-space: nowrap;

        // Use attribute selectors instead of class selectors to avoid circular dependencies
        &[class*="text-blue"] {
          @apply bg-blue-100/80;
        }

        &[class*="text-blue-600"] {
          @apply bg-blue-100/90;
        }
      }
    }

    // Search results styling
    .search-results-active & {
      .p-treenode-content {
        @apply hover:bg-amber-100/70 hover:border-amber-200;

        &:hover {
          @apply shadow-md;
          animation: searchItemHover 0.3s ease;
        }
      }

      // PDF files in search results
      [ptemplate="pdf"] .p-treenode-content {
        @apply bg-white/80 border-amber-100;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          @apply absolute inset-0 bg-gradient-to-r from-amber-50/30 to-transparent;
          transform: translateX(-100%);
          animation: searchHighlight 2s ease-in-out;
          animation-delay: calc(var(--tree-node-index, 0) * 0.1s);
        }

        &:hover::before {
          animation: none;
        }
      }
    }
  }

  @keyframes searchHighlight {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0); }
    100% { transform: translateX(100%); }
  }

  @keyframes searchItemHover {
    0% { transform: translateY(0); }
    50% { transform: translateY(-2px); }
    100% { transform: translateY(-1px); }
  }

  // Icon colors
  .pi-folder    { @apply text-amber-500; }
  .pi-file-edit { @apply text-blue-600; }
  .pi-image     { @apply text-blue-400; }
  .pi-file-pdf  { @apply text-red-600 font-bold; }
  .pi-file      { @apply text-blue-600; }

  // Icon colors for components
  .file-metadata .icon { @apply text-lg; }

  // Toggler styling
  .p-treenode-toggler {
    @apply hover:bg-blue-200 rounded-full;
    transition: all 0.2s ease;
    &.pi-chevron-down { @apply rotate-90; }

    &:hover {
      @apply transform scale-110;
    }
  }

  // Button styles
  .btn-action {
    @apply hover:bg-blue-200 text-blue-800 rounded-full w-9 h-9 flex items-center justify-center;
    transition: all 0.2s ease;

    &:hover {
      @apply transform scale-105 shadow-sm;
    }

    &:disabled {
      @apply opacity-50 cursor-not-allowed;
    }

    i {
      @apply text-lg;
    }
  }

  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white rounded-full px-3 py-1.5;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      @apply transform scale-105 shadow-md;
    }

    &:disabled {
      @apply opacity-60 cursor-not-allowed bg-gray-400;
    }
  }

  .btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white rounded-full px-3 py-1.5;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      @apply transform scale-105 shadow-md;
    }

    &:disabled {
      @apply opacity-60 cursor-not-allowed bg-gray-400;
    }
  }

  .btn-secondary {
    @apply bg-blue-200 hover:bg-blue-300 text-blue-700 rounded-full px-3 py-1.5;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      @apply transform scale-105 shadow-md;
    }

    &:disabled {
      @apply opacity-60 cursor-not-allowed;
    }
  }

  // Load more buttons
  .load-more-container {
    @apply flex items-center justify-between w-52 whitespace-nowrap px-1.5 py-0.5 border border-blue-200 bg-white/80 text-xs rounded-lg shadow-sm; /* Reduced size and padding */
    backdrop-filter: blur(4px);
  }

  .load-more-btn {
    @apply inline-flex items-center cursor-pointer px-2 py-0.5 font-medium text-blue-700 hover:text-blue-500 hover:bg-blue-100/70 rounded-md; /* Reduced padding */
    transition: all 0.15s ease;

    &:hover {
      @apply transform scale-105 shadow-sm;
    }

    &:first-child {
      @apply bg-blue-50/80;
    }

    &:last-child {
      @apply bg-blue-100/80;
    }
  }

  // Selected files section
  .selected-files-section {
    @apply border-t border-blue-300 bg-gradient-to-b from-blue-100 to-blue-50 flex flex-col h-64;
    box-shadow: 0 -4px 6px -1px rgba(37, 99, 235, 0.05);
  }

  .selected-files-header {
    @apply p-4 border-b border-blue-200 sticky top-0 z-10 bg-blue-100 shadow-sm flex items-center justify-between;
    background: linear-gradient(to right, #DBEAFE, #EFF6FF);
  }

  .action-buttons {
    @apply flex gap-1.5;
  }

  .selected-files-content {
    @apply p-4 pt-2 overflow-y-auto flex-1 flex flex-col gap-2;
  }

  .file-item {
    @apply flex items-center p-3 rounded-xl bg-white text-blue-800 shadow-sm border border-blue-200 hover:bg-blue-50;
    transition: all 0.2s ease;

    &:hover {
      @apply shadow-md border-blue-300 transform -translate-y-px;
    }

    // File type badge
    &::after {
      content: attr(data-filetype);
      @apply ml-2 text-xs font-medium px-1.5 py-0.5 rounded-full bg-blue-100 text-blue-700;
    }
  }

  .no-files-message {
    @apply text-sm text-blue-600 p-3 bg-white rounded-xl shadow-sm border border-blue-200;
  }

  // Modern action menu styling
  ::ng-deep .action-menu-modern {
    @apply rounded-xl shadow-xl border border-gray-100 min-w-[220px];

    .p-menu-list {
      @apply p-1.5;
    }

    .p-menuitem {
      @apply rounded-lg overflow-hidden my-0.5;

      .p-menuitem-link {
        @apply rounded-lg px-3 py-2;

        &:hover {
          @apply bg-gray-50;
        }

        .p-menuitem-icon {
          @apply text-gray-500 mr-3;
        }

        .p-menuitem-text {
          @apply text-sm text-gray-700;
        }

        &:hover .p-menuitem-icon {
          @apply text-blue-600;
        }

        &.p-disabled {
          @apply opacity-50;
        }
      }
    }
  }

  // Empty state
  .empty-state {
    @apply flex items-center justify-center h-full;
  }

  .empty-state-content {
    @apply flex flex-col items-center justify-center p-8 rounded-2xl bg-white/80 shadow-lg border border-blue-100;
    animation: fadeInUp 0.5s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // Scrollbar styling
  .tree-container, .selected-files-content {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.4) transparent;

    &::-webkit-scrollbar {
      @apply w-2;
    }

    &::-webkit-scrollbar-track {
      @apply bg-transparent;
    }

    &::-webkit-scrollbar-thumb {
      @apply bg-blue-200 rounded-full;

      &:hover {
        @apply bg-blue-300;
      }
    }
  }

  // File status indicator
  .file-status {
    @apply inline-flex items-center justify-center h-3 w-3 rounded-full;

    &.status-normal {
      @apply bg-green-400;
    }

    &.status-warning {
      @apply bg-yellow-400;
    }

    &.status-error {
      @apply bg-red-400;
    }
  }

  // Notification indicator
  .notification-indicator {
    @apply inline-flex items-center justify-center h-5 w-5 rounded-full bg-yellow-500 text-white font-bold text-xs;
    animation: pulse 1.5s infinite;
  }

  // Search result indicator
  .search-result-indicator {
    @apply absolute right-0 top-0 bottom-0 w-1 bg-amber-400 rounded-r-lg;
    animation: pulseGlow 2s infinite;
  }

  @keyframes pulseGlow {
    0% { box-shadow: 0 0 0 0 rgba(251, 191, 36, 0.4); }
    70% { box-shadow: 0 0 0 6px rgba(251, 191, 36, 0); }
    100% { box-shadow: 0 0 0 0 rgba(251, 191, 36, 0); }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.9;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  // File metadata
  .file-metadata {
    @apply text-xs text-blue-600 flex items-center gap-1.5;

    .metadata-item {
      @apply flex items-center;

      .icon {
        @apply mr-1 text-blue-400;
      }
    }
  }
}

// PDF Viewer container styles for the file-system component
.pdf-viewer-container {
  width: 66.667%; /* Changed from 49% to 2/3 */
  transition: width 0.3s ease;
  height: calc(100vh - 4rem); // Match the height of the tree view
  flex-grow: 1; /* Allow growing to fill available space */
  min-width: 400px; /* Ensure minimum width for usability */

  @media (max-width: 768px) {
    display: none;
  }
}

/* Search results toggle button */
.search-results-toggle-btn {
  @apply flex items-center justify-center w-9 h-9 rounded-full bg-white shadow-sm border border-blue-200 text-blue-700 hover:bg-blue-50 transition-colors relative;

  &.active {
    @apply bg-blue-600 text-white border-blue-700 hover:bg-blue-700;
  }

  i {
    @apply text-lg;
  }
}

.search-count-indicator {
  @apply absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 rounded-full bg-amber-500 text-white text-xs font-bold;
}

/* Search tabs wrapper with transitions */
.search-tabs-wrapper {
  @apply sticky top-0 z-20 transition-all duration-300 ease-in-out overflow-hidden;
  margin: 0;
  padding: 0;

  &.visible {
    max-height: 2.5rem; /* 40px */
    opacity: 1;
  }

  &.hidden {
    max-height: 0;
    opacity: 0;
  }

  &.no-tabs {
    display: none;
  }
}

/* Tabs styling */
.search-tabs-container {
  @apply sticky top-0 z-20 bg-white border-b border-blue-200;
  margin: 0;
  padding: 0;

  ::ng-deep {
    .p-tablist {
      @apply bg-white border-b border-blue-200 shadow-sm;
      margin: 0;
      padding: 0;
    }

    .p-tab {
      @apply py-1 px-4 text-sm font-medium text-blue-700 hover:text-blue-900 transition-colors;
      height: 2.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      &:hover {
        @apply bg-blue-50;
      }

      &.p-highlight {
        @apply text-blue-900 bg-blue-100/50 border-b-2 border-blue-500;

        .viewmode-badge {
          opacity: 1;
        }
      }
    }

    .p-tablist-active-bar {
      @apply bg-blue-500;
    }
  }
}

.tab-close-btn {
  @apply flex items-center justify-center w-5 h-5 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-800;
  transition: all 0.2s ease;
  z-index: 2 !important;

  &:hover {
    @apply transform rotate-90;
  }

  i {
    @apply text-xs;
  }
}

/* Hidden tabs indicator */
.hidden-tabs-indicator {
  @apply inline-flex items-center ml-3 py-0.5 px-2 rounded-full bg-amber-100 text-amber-800 text-xs font-medium;
}

/* View mode with hidden tabs */
.view-mode-indicator {
  &.with-hidden-tabs {
    @apply bg-gradient-to-r from-blue-50 to-amber-50 border-amber-200;
  }
}

/* View mode actions container */
.view-mode-actions {
  @apply flex items-center gap-2;
}

/* Show tabs button */
.show-tabs-btn {
  @apply flex items-center py-1 px-2 rounded bg-amber-100 text-amber-800 text-sm font-medium hover:bg-amber-200 transition-colors;
}

// Show saved searches button
.show-saved-searches-btn {
  @apply inline-flex items-center py-1 px-2.5 rounded-full bg-amber-100 text-amber-800 text-xs font-medium;
  transition: all 0.2s ease;

  &:hover {
    @apply bg-amber-200 shadow-sm;
  }

  &:active {
    @apply bg-amber-300 transform scale-95;
  }

  &:focus {
    @apply outline-none ring-2 ring-amber-300;
  }
}
.p-tab > .p-button-text { z-index: 5; }

// Viewmode badge styling - simple, modern and compact
.viewmode-badge {
  @apply inline-flex items-center justify-center px-1.5 py-0.5 ml-1.5 text-xs font-medium rounded;
  background-color: #3b82f6;
  color: white;
  transition: transform 0.15s ease;
  position: relative;
  text-align: center;
  font-size: 0.7rem;
  line-height: 1;

  &:hover {
    transform: translateY(-1px);
  }

  // Different colors based on view mode - flat colors for modern look
  &.viewmode-status {
    background-color: #3b82f6; // Blue
  }

  &.viewmode-date {
    background-color: #8b5cf6; // Purple
  }

  &.viewmode-case {
    background-color: #10b981; // Green
  }
}
