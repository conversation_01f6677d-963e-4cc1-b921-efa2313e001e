<div class="file-system-container h-screen" [ngClass]="{'search-results-active': showSearchResults}">
  <div class="file-system shadow-xl rounded-2xl ring-1 ring-blue-300 flex flex-col" [ngClass]="{'file-system-with-pdf': selectedFilePath !== null}">

  <div class="toolbar flex items-center gap-2 p-2 border-b border-blue-300 bg-gradient-to-r from-blue-100 to-blue-50">
    <!-- View Mode Selector -->
    <div class="view-mode-selector flex items-center gap-2 mr-4">
      <span class="text-sm font-medium text-blue-700">View:</span>
      <div class="flex bg-white rounded-lg shadow-sm border border-blue-200 overflow-hidden">
        <button *ngFor="let newMode of availableViewModes"
                class="view-mode-btn px-3 py-1.5 text-sm font-medium transition-colors"
                [ngClass]="{
                  'bg-blue-600 text-white': newMode === viewMode,
                  'bg-white text-blue-700 hover:bg-blue-50': newMode !== viewMode
                }"
                (click)="state.changeViewMode(newMode)">
          {{ getViewModeLabel(newMode) }}
        </button>
      </div>
    </div>

    <button
      class="p-button p-button-sm p-button-text btn-action"
      type="button"
      [disabled]="state.isLoading"
      (click)="state.expandRoot()"
      title="Refresh Files">
      <i class="pi pi-sync text-blue-600"></i>
    </button>

    <button
      class="p-button p-button-sm p-button-text btn-action"
      type="button"
      [disabled]="state.selectedNodes.length === 0"
      (click)="state.selectedNodes = []"
      title="Clear Selection">
      <i class="pi pi-times-circle text-blue-600"></i>
    </button>

    <!-- Search Button - Opens Search Form -->
    <button
      class="search-toggle-btn ml-auto"
      type="button"
      (click)="searchFormVisible = !searchFormVisible"
      [ngClass]="{'active': searchFormVisible}"
      title="Search Files (Ctrl+F)">
      <i class="pi pi-search"></i>
      <div class="search-btn-glow"></div>
    </button>

    <!-- Toggle Search Results Button -->
    <button
      *ngIf="hasSearchViews"
      class="search-results-toggle-btn"
      type="button"
      (click)="toggleSearchTabsVisibility()"
      [ngClass]="{'active': searchTabsVisible && showSearchResults}"
      [title]="searchTabsVisible ? 'Hide Search Results' : 'Show Search Results'">
      <i class="pi" [ngClass]="{'pi-eye': searchTabsVisible, 'pi-eye-slash': !searchTabsVisible}"></i>
      <div class="search-count-indicator">{{ searchViews.length }}</div>
    </button>

    <div *ngIf="state.selectedNodes.length > 0" class="flex-container items-center gap-2 px-3 py-1.5 bg-blue-100/70 rounded-full shadow-sm border border-blue-200">
      <i class="pi pi-check-circle text-blue-600"></i>
      <span class="text-sm font-medium text-blue-700">{{ state.selectedNodes.length }} item(s) selected</span>
    </div>
  </div>

  <!-- Search Component -->
  <app-file-search
    [visible]="searchFormVisible"
    [searching]="state.searchInProgress"
    [(searchCriteria)]="currentSearchCriteria"
    (search)="state.performSearch($event)"
    (cancel)="searchFormVisible = false">
  </app-file-search>



  <div class="tree-container flex-grow overflow-y-auto relative" [ngClass]="{'search-results-active': showSearchResults}">
    <!-- Tabbed interface for search views -->
    <div class="search-tabs-wrapper" [ngClass]="{'visible': searchTabsVisible && hasSearchViews, 'hidden': !searchTabsVisible && hasSearchViews, 'no-tabs': !hasSearchViews}">
      <p-tabs *ngIf="hasSearchViews"
              [scrollable]="true"
              [(value)]="activeTabAsString"
              class="search-tabs-container">
        <p-tablist>
          <!-- Search result tabs -->
          <ng-container *ngFor="let id of searchViews">
            <p-tab  [value]="id.toString()" class="flex items-center !gap-2">
              <i class="pi pi-search text-blue-500"></i>
              <span class="font-medium">{{ id.searchCriteria?.formatted() || 'Search Results' }}</span>
              <span class="viewmode-badge" [ngClass]="'viewmode-' + id.viewMode">{{ getViewModeLabel(id.viewMode) }}</span>
              <button
                class="tab-close-btn ml-2 p-button-text"
                (pointerdown)="$event.stopPropagation()"
                (click)="$event.stopPropagation(); closeSearchView(id);">
                <i class="pi pi-times"></i>
              </button>
            </p-tab>
          </ng-container>
        </p-tablist>
      </p-tabs>
    </div>

    <!-- View mode indicator -->
    <div class="view-mode-indicator"
         [ngClass]="{
           'search-results-active': showSearchResults,
           'with-tabs': hasSearchViews && searchTabsVisible,
           'with-hidden-tabs': hasSearchViews && !searchTabsVisible
         }">
      <div class="view-mode-content w-full">
        <div class="view-mode-info flex items-center w-full justify-between">
          <div class="flex items-center">
            <div class="view-mode-icon">
              <i class="pi" [ngClass]="{
                'pi-eye': !showSearchResults,
                'pi-search': showSearchResults
              }"></i>
            </div>
            <div class="view-mode-text flex flex-col">
              <span class="view-mode-label">Current View: <strong>{{ currentViewModeLabel }}</strong></span>
              <span *ngIf="!showSearchResults && viewMode === 'status'" class="view-mode-description">(Files organized by status)</span>
              <span *ngIf="!showSearchResults && viewMode === 'date'" class="view-mode-description">(Files organized by date)</span>
              <span *ngIf="!showSearchResults && viewMode === 'case'" class="view-mode-description">(Files organized by case)</span>
              <span *ngIf="showSearchResults" class="view-mode-description search-results-text">
                <span class="search-results-count">{{ getSearchResultsCount() }}</span> results found
              </span>
            </div>
          </div>
          <button *ngIf="hasSearchViews && !searchTabsVisible"
                class="show-saved-searches-btn"
                (click)="toggleSearchTabsVisibility()"
                title="Show saved searches">
            <i class="pi pi-search-plus text-amber-500 mr-1.5"></i>
            <span>{{ searchViews.length }} saved
              {{ searchViews.length === 1 ? 'search' : 'searches' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading overlay for tree view only -->
    <div *ngIf="state.isLoading || state.searchInProgress" class="tree-loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner-large"></div>
        <h3 class="loading-text">{{ state.searchInProgress ? 'Searching Files' : 'Loading File System' }}</h3>
        <div class="loading-progress">
          <div class="loading-progress-bar" [ngClass]="{'search-progress': state.searchInProgress}"></div>
        </div>
      </div>
    </div>

    <p-tree
      *ngIf="!state.isLoading"
      [value]="[model!.root]"
      selectionMode="multiple"
      [metaKeySelection]="true"
      [(selection)]="state.selectedNodes"
      (onNodeSelect)="state.onNodeSelect($event)"
      (onNodeExpand)="expandByKey($event.node.key)"
      class="file-tree"
      [ngClass]="{'search-results-active': showSearchResults}">
      <!-- Directory node -->
      <ng-template pTemplate="folder" let-node>
        <div class="flex items-center w-full"
             [ngClass]="{ 'cursor-pointer': !node.data?.isLoading, 'cursor-wait': node.data?.isLoading }">
          <i *ngIf="!node.loading" class="pi pi-folder mr-2 text-amber-500 text-lg font-bold" ></i>
          <div *ngIf="node.loading" class="loading-spinner mr-2 h-4 w-4"></div>

          <span class="font-medium text-blue-800 hover:text-blue-600">
            {{ node.label }}
            <small *ngIf="node.loading" class="text-blue-500 ml-2">(loading…)</small>
            <small *ngIf="!node.loading && node.data?.totalElementCount !== undefined"
                   [ngClass]="{
                     'text-blue-400': node.data.loadedElementCount < node.data?.totalElementCount,
                     'text-blue-600': node.data.loadedElementCount >= node.data?.totalElementCount
                   }"
                   class="ml-2">
              ({{ node.data.loadedElementCount || 0 }}/{{ node.data?.totalElementCount }} items)
            </small>
          </span>
        </div>
      </ng-template>
      <!-- PDF file node -->
      <ng-template pTemplate="pdf" let-node let-i="index">
        <div class="flex items-center w-full" [attr.style]="'--tree-node-index: ' + i">
          <i class="pi pi-file-pdf mr-2 text-red-600 text-lg font-bold"></i>
          <span [ngClass]="{'crossed-text': node.data?.isCrossed, 'red-text': node.data?.isRed}">{{ node.label }}</span>

          <!-- Notification indicator -->
          <span *ngIf="node.data?.hasNotification" class="notification-indicator ml-1" title="New notification">!</span>

          <!-- File status indicator based on file state -->
          <span class="file-status ml-3"
                [ngClass]="{
                  'status-normal': !node.data?.isCrossed && !node.data?.isRed,
                  'status-warning': node.data?.isCrossed && !node.data?.isRed,
                  'status-error': node.data?.isRed
                }"></span>

          <!-- File size info -->
          <span class="ml-auto pl-3 text-xs" [ngClass]="{'text-blue-500': !showSearchResults, 'text-amber-500': showSearchResults}">{{ node.data?.size | number }} B</span>

          <!-- Search result highlight indicator -->
          <div *ngIf="showSearchResults" class="search-result-indicator"></div>
        </div>
      </ng-template>

      <!-- Load more or load all buttons node template -->
      <ng-template pTemplate="loadMore" let-node>
        <div class="load-more-container">
          <ng-container *ngIf="!node.parent.loading; else loading">
            <div class="load-more-btn" (click)="state.loadAllFromServer(node.parent.key)">
              <i class="pi pi-list mr-1.5"></i>
              <span>Show all</span>
            </div>
            <div class="load-more-btn ml-auto" (click)="state.loadNextPage(node.parent.key)">
              <i class="pi pi-plus mr-1.5"></i>
              <span>Show 10 more</span>
            </div>
          </ng-container>
          <ng-template #loading>
            <div class="flex items-center justify-center w-full">
              <div class="loading-spinner mr-1.5 h-3 w-3"></div>
              <span class="font-medium text-blue-800">Loading...</span>
            </div>
          </ng-template>
        </div>
      </ng-template>

    </p-tree>
  </div>


  </div>

  <!-- PDF Viewer Tabs Section with File Actions Above -->
  <div *ngIf="selectedFilePath !== null" class="pdf-section">
    <div class="pdf-viewer-wrapper h-full">
      <!-- File Actions Component -->
      <div class="w-full flex-none">
        <app-file-actions
          [selectedNodes]="state.selectedNodes"
          class="file-actions-above-pdf shadow-xl rounded-2xl ring-1 ring-blue-300">
        </app-file-actions>
      </div>

      <!-- Spacer Element with Visual Separator -->
      <div class="h-3 w-full flex items-center">
        <div class="flex-grow border-t border-blue-100"></div>
      </div>

      <!-- PDF Viewer Tabs Component -->
      <div class="w-full flex-grow">
        <app-pdf-viewer-tabs
          [selectedFilePath]="selectedFilePath"
          (close)="selectedFilePath = null"
          class="pdf-viewer-container shadow-xl rounded-2xl ring-1 ring-blue-300">
        </app-pdf-viewer-tabs>
      </div>
    </div>
  </div>
</div>
