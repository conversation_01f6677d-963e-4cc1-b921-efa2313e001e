import {MenuItem, TreeNode} from 'primeng/api';
import {FileData} from '../../../state/FileSystemEntry';

export interface FileSystemCommand {
  execute(node: TreeNode<FileData>[]): void;
}

export const actionItems: MenuItem[] = [
  {
    label: 'File Operations',
    items: [
      {
        label: 'Rename',
        icon: 'pi pi-pencil',
        command: () => {},
        disabled: false
      },
      {
        label: 'Move to...',
        icon: 'pi pi-arrows-alt',
        command: () => {},
        disabled: false
      },
      {
        label: 'Copy to...',
        icon: 'pi pi-copy',
        command: () => {},
        disabled: false
      }
    ]
  },
  {
    label: 'Sharing',
    items: [
      {
        label: 'Share via Link',
        icon: 'pi pi-link',
        command: () => {},
        disabled: false
      },
      {
        label: 'Share with Users',
        icon: 'pi pi-users',
        command: () => {},
        disabled: false
      }
    ]
  },
  {
    label: 'Tools',
    items: [
      {
        label: 'Properties',
        icon: 'pi pi-info-circle',
        command: () => {},
        disabled: false
      },
      {
        label: 'Tags',
        icon: 'pi pi-tags',
        command: () => {},
        disabled: false
      }
    ]
  }
];
