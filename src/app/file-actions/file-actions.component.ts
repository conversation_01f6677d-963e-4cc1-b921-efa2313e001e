import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import {MenuItem, TreeNode} from 'primeng/api';

@Component({
  selector: 'app-file-actions',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    MenuModule
  ],
  templateUrl: './file-actions.component.html',
  styleUrl: './file-actions.component.scss'
})
export class FileActionsComponent {
  @Input() selectedNodes: TreeNode[] = [];

  // Menu items for file operations
  fileOperationsItems: MenuItem[] = [
    {
      label: 'Rename',
      icon: 'pi pi-pencil',
      command: () => {
        console.log('Rename action');
      }
    },
    {
      label: 'Delete',
      icon: 'pi pi-trash',
      command: () => {
        console.log('Delete action');
      }
    },
    {
      label: 'Move',
      icon: 'pi pi-arrows-alt',
      command: () => {
        console.log('Move action');
      }
    },
    {
      label: 'Copy',
      icon: 'pi pi-copy',
      command: () => {
        console.log('Copy action');
      }
    }
  ];

  // Menu items for sharing
  sharingItems: MenuItem[] = [
    {
      label: 'Share Link',
      icon: 'pi pi-link',
      command: () => {
        console.log('Share Link action');
      }
    },
    {
      label: 'Email',
      icon: 'pi pi-envelope',
      command: () => {
        console.log('Email action');
      }
    },
    {
      label: 'Download',
      icon: 'pi pi-download',
      command: () => {
        console.log('Download action');
      }
    }
  ];

  // Menu items for tools
  toolsItems: MenuItem[] = [
    {
      label: 'Mark as Reviewed',
      icon: 'pi pi-check-circle',
      command: () => {
        console.log('Mark as Reviewed action');
      }
    },
    {
      label: 'Flag for Review',
      icon: 'pi pi-flag',
      command: () => {
        console.log('Flag for Review action');
      }
    },
    {
      label: 'Add to Case',
      icon: 'pi pi-folder-open',
      command: () => {
        console.log('Add to Case action');
      }
    }
  ];
}
