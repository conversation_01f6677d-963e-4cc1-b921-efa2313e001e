@charset "UTF-8";
﻿﻿﻿﻿.file-actions-container {
  @apply w-full;
  display: block;
  max-width: 100%; /* Prevent overflow */
}

.file-actions-box {
  @apply bg-white overflow-hidden;
  transition: all 0.2s ease;
  width: 100%;
  max-width: 100%; /* Prevent overflow */
}

.file-actions-content {
  @apply p-3 flex flex-wrap gap-2;
  border-radius: 1rem; /* Match the PDF viewer's border-radius */
}

.action-indicator {
  @apply flex items-center mr-3;
}
.action-indicator .selected-files-text {
  @apply text-blue-700 text-sm font-medium;
}

.file-count-badge {
  @apply ml-2 px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs font-semibold;
}

.action-buttons {
  @apply flex flex-wrap gap-2;
}

.action-dropdown-btn {
  @apply flex items-center px-3 py-1.5 bg-white rounded-lg border border-blue-200 text-blue-700 text-sm font-medium shadow-sm hover:bg-blue-50 hover:border-blue-300 transition-all;
}
.action-dropdown-btn:hover {
  @apply shadow-md transform -translate-y-px;
}
.action-dropdown-btn .pi {
  @apply text-blue-500;
}

:host ::ng-deep .action-menu-modern {
  @apply rounded-xl shadow-xl border border-gray-100 min-w-[220px];
}
:host ::ng-deep .action-menu-modern .p-menu-list {
  @apply p-1.5;
}
:host ::ng-deep .action-menu-modern .p-menuitem {
  @apply rounded-lg overflow-hidden my-0.5;
}
:host ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link {
  @apply rounded-lg px-3 py-2;
}
:host ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link:hover {
  @apply bg-gray-50;
}
:host ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link .p-menuitem-icon {
  @apply text-gray-500 mr-3;
}
:host ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link .p-menuitem-text {
  @apply text-sm text-gray-700;
}
:host ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link:hover .p-menuitem-icon {
  @apply text-blue-600;
}
:host ::ng-deep .action-menu-modern .p-menuitem .p-menuitem-link.p-disabled {
  @apply opacity-50;
}

/*# sourceMappingURL=file-actions.component.css.map */
