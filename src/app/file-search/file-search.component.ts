import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormControl, FormGroup, ReactiveFormsModule} from '@angular/forms';
import {DatePickerModule} from 'primeng/datepicker';
import {InputTextModule} from 'primeng/inputtext';
import {SelectModule} from 'primeng/select';
import {ButtonModule} from 'primeng/button';
import {animate, style, transition, trigger} from '@angular/animations';
import {ISearchCriteria, SearchCriteria} from './search.criteria';

@Component({
  selector: 'app-file-search',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DatePickerModule,
    InputTextModule,
    SelectModule,
    ButtonModule
  ],
  templateUrl: './file-search.component.html',
  styleUrl: './file-search.component.scss',
  animations: [
    trigger('searchFormAnimation', [
      transition(':enter', [
        style({ height: 0, opacity: 0, overflow: 'hidden' }),
        animate('300ms cubic-bezier(0.4, 0, 0.2, 1)',
          style({ height: '*', opacity: 1 }))
      ]),
      transition(':leave', [
        style({ height: '*', opacity: 1, overflow: 'hidden' }),
        animate('200ms cubic-bezier(0.4, 0, 0.2, 1)',
          style({ height: 0, opacity: 0 }))
      ])
    ])
  ]
})
export class FileSearchComponent implements OnInit {
  private _searchCriteria: SearchCriteria = SearchCriteria.empty();

  @Input() visible = false;
  @Input() searching = false;
  @Input() set searchCriteria(value: SearchCriteria) {
    this._searchCriteria = value;
    this.updateFormFromCriteria(value);
  }
  @Output() searchCriteriaChange = new EventEmitter<SearchCriteria>();
  @Output() search = new EventEmitter<SearchCriteria>();
  @Output() cancel = new EventEmitter<void>();

  readonly statusOptions = [
    { label: 'Any Status', value: '' },
    { label: 'Pending', value: 'pending' },
    { label: 'Approved', value: 'approved' },
    { label: 'Rejected', value: 'rejected' },
    { label: 'Under Review', value: 'review' }
  ];

  searchForm = new FormGroup({
    dateRange: new FormControl<Date[] | null>(null),
    status: new FormControl(''),
    caseId: new FormControl('')
  });

  get searchCriteria(): SearchCriteria {
    return this._searchCriteria;
  }

  private updateFormFromCriteria(criteria: SearchCriteria): void {
    // Create date range only if we have both dates
    let dateRange = null;
    if (criteria.fromCreationDate && criteria.toCreationDate) {
      dateRange = [
        new Date(criteria.fromCreationDate),
        new Date(criteria.toCreationDate)
      ];
    } else if (criteria.fromCreationDate) {
      dateRange = [new Date(criteria.fromCreationDate)];
    }

    // Update the form with the new values
    this.searchForm.patchValue({
      dateRange,
      status: criteria.status || '',
      caseId: criteria.caseId || ''
    }, { emitEvent: false });
  }

  private createCriteriaFromForm(): ISearchCriteria {
    const { dateRange, status, caseId } = this.searchForm.value;

    // Extract dates from the range
    let fromCreationDate: Date | undefined = undefined;
    let toCreationDate: Date | undefined = undefined;

    if (dateRange && Array.isArray(dateRange)) {
      if (dateRange.length >= 1 && dateRange[0]) {
        fromCreationDate = new Date(dateRange[0]);
      }

      if (dateRange.length >= 2 && dateRange[1]) {
        toCreationDate = new Date(dateRange[1]);
      }
    }

    return {
      fromCreationDate,
      toCreationDate,
      status: status || undefined,
      caseId: caseId || undefined
    };
  }

  getStatusLabel(value: string): string {
    const option = this.statusOptions.find(opt => opt.value === value);
    return option ? option.label : 'Any Status';
  }

  ngOnInit(): void {
    // Listen for form value changes
    this.searchForm.valueChanges.subscribe(() => {
      const newCriteria = SearchCriteria.from(this.createCriteriaFromForm());

      // Only emit if the criteria has actually changed
      if (!this._searchCriteria.equals(newCriteria)) {
        this._searchCriteria = newCriteria;
        this.searchCriteriaChange.emit(this._searchCriteria);
      }
    });

    // Initialize the form with the current criteria
    if (this._searchCriteria) {
      this.updateFormFromCriteria(this._searchCriteria);
    }
  }

  onSubmit(): void {
    if (this.searching) return;
    this.search.emit(this._searchCriteria);
  }

  isDateInRange(date: any): boolean {
    const dateRange = this.searchForm.get('dateRange')?.value;
    if (!dateRange || !Array.isArray(dateRange) || dateRange.length < 2) return false;
    if (!dateRange[0] || !dateRange[1]) return false;

    // Convert the date from the template (which has month as 0-based index)
    const checkDate = new Date(date.year, date.month, date.day);
    const startDate = new Date(dateRange[0]);
    const endDate = new Date(dateRange[1]);

    // Reset time components for accurate comparison
    checkDate.setHours(0, 0, 0, 0);
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(0, 0, 0, 0);

    return checkDate >= startDate && checkDate <= endDate;
  }
}
