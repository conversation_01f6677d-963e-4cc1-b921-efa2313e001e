<div *ngIf="visible" [@searchFormAnimation] class="search-form-container border-b border-blue-300">
  <div class="search-form-content">
    <div class="search-form-header">
      <h3 class="search-form-title">
        <i class="pi pi-search mr-2"></i>
        Search Files
      </h3>
      <button type="button" class="search-form-close" (click)="cancel.emit()">
        <i class="pi pi-times"></i>
      </button>
    </div>

    <form [formGroup]="searchForm" (ngSubmit)="onSubmit()" class="search-form-grid">
      <div class="search-form-compact-layout">
        <!-- Case ID and Status Row -->
        <div class="search-form-row">
          <!-- Case ID -->
          <div class="search-form-group">
            <label for="caseId" class="search-form-label">
              <i class="pi pi-id-card mr-1"></i>
              Case ID
            </label>
            <div class="search-input-container case-id-container">
              <input id="caseId" type="text" pInputText formControlName="caseId"
                    placeholder="Enter case ID" class="case-id-input w-full">
            </div>
          </div>

          <!-- Status Dropdown -->
          <div class="search-form-group">
            <label for="status" class="search-form-label">
              <i class="pi pi-tag mr-1"></i>
              Status
            </label>
            <div class="search-input-container status-dropdown-container">
              <p-select id="status" formControlName="status"
                        [options]="statusOptions"
                        placeholder="Select status"
                        styleClass="status-dropdown w-full"
                        optionLabel="label"
                        optionValue="value"
                        [appendTo]="'body'">
                <ng-template pTemplate="selectedItem" let-selectedOption>
                  <div *ngIf="selectedOption" class="status-item-selected">
                    <span class="status-indicator" [ngClass]="'status-' + selectedOption.value"></span>
                    <span class="status-text">{{ selectedOption.label }}</span>
                  </div>
                  <div *ngIf="!selectedOption" class="status-placeholder">
                    Select status
                  </div>
                </ng-template>
                <ng-template pTemplate="item" let-option>
                  <div class="status-item">
                    <span class="status-indicator" [ngClass]="'status-' + option.value"></span>
                    <span class="status-text">{{ option.label }}</span>
                  </div>
                </ng-template>
              </p-select>
            </div>
          </div>
        </div>

        <!-- Unified Date Range -->
        <div class="search-form-group date-range-group">
          <label class="search-form-label">
            <i class="pi pi-calendar mr-1"></i>
            Date Range
          </label>
          <div class="search-input-container calendar-container">
              <p-datepicker formControlName="dateRange"
                        selectionMode="range"
                        [showIcon]="true"
                        [showButtonBar]="true"
                        placeholder="Select date range"
                        styleClass="date-range-calendar w-full"
                        [showClear]="true"
                        dateFormat="yy-mm-dd"
                        [appendTo]="'body'"
                        [numberOfMonths]="1"
                        [hideOnDateTimeSelect]="false">
              </p-datepicker>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="search-form-actions">
        <button type="submit" class="search-submit-btn" [disabled]="searching">
          <i *ngIf="!searching" class="pi pi-search mr-1"></i>
          <div *ngIf="searching" class="search-spinner mr-1"></div>
          {{ searching ? 'Searching...' : 'Search' }}
        </button>
        <button type="button" class="search-reset-btn" (click)="searchForm.reset()" [disabled]="searching">
          <i class="pi pi-refresh mr-1"></i>
          Reset
        </button>
        <button type="button" class="search-cancel-btn" (click)="cancel.emit()" [disabled]="searching">
          <i class="pi pi-times mr-1"></i>
          Cancel
        </button>
      </div>
    </form>
  </div>
</div>
