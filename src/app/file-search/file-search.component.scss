// Search form container
.search-form-container {
  @apply overflow-hidden bg-gradient-to-br from-blue-50 to-white;
}

// Search form content
.search-form-content {
  @apply p-3; // Reduced padding for more compact appearance
}

// Search form header
.search-form-header {
  @apply flex items-center justify-between mb-2; // Reduced margin for more compact appearance
}

.search-form-title {
  @apply text-lg font-bold text-blue-700 flex items-center;

  i {
    @apply text-blue-500;
  }
}

.search-form-close {
  @apply flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200;
  transition: all 0.2s ease;
  border: none;

  &:hover {
    @apply transform rotate-90;
  }
}

// Search form grid
.search-form-grid {
  @apply flex flex-col gap-2; // Reduced gap for more compact appearance
}

// Compact layout container
.search-form-compact-layout {
  @apply flex flex-col gap-2;
}

// Search form rows
.search-form-row {
  @apply grid grid-cols-2 gap-2; // Reduced gap for more compact appearance
}

// Date range group
.date-range-group {
  @apply mt-1; // Add a small margin to separate from the row above
}

// Search form groups
.search-form-group {
  @apply flex flex-col;
}

.search-form-label {
  @apply flex items-center text-xs font-medium text-blue-700 mb-0.5; // Reduced margin for more compact appearance

  i {
    @apply text-blue-500;
  }
}

// Search input containers
.search-input-container {
  @apply relative;

  &.calendar-container {
    @apply w-full;

    // Calendar container styling

    ::ng-deep .p-datepicker {
      @apply w-full h-[36px]; // Fixed height to match other inputs
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-1px);
      }

      // Style for range selection
      &.date-range-calendar {
        @apply w-full;

        .p-inputtext {
          @apply py-1.5 px-3 h-[36px] border border-blue-200 rounded-lg;
          transition: all 0.2s ease;

          &:focus {
            @apply border-blue-400 shadow-sm;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
          }

          &:hover:not(:focus) {
            @apply border-blue-300;
          }
        }

        .p-datepicker-trigger {
          @apply bg-blue-600 border-blue-600 rounded-r-lg;
          transition: all 0.2s ease;

          &:hover {
            @apply bg-blue-500 border-blue-500;
          }

          &:focus {
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
          }

          .pi-calendar {
            @apply text-white;
          }
        }
      }

      .p-inputtext {
        @apply py-1.5 px-2 h-[36px];
        font-size: 0.8125rem; // Match the size of the dropdown label
        box-sizing: border-box;
      }

      // Style the clear icon to look better
      .p-calendar-clear-icon {
        @apply text-gray-400 hover:text-gray-600;
        position: absolute;
        right: 36px; // Position it before the calendar icon
        top: 50%;
        transform: translateY(-50%);
        font-size: 0.75rem;
        cursor: pointer;
        z-index: 1;
      }

      .p-datepicker-trigger {
        @apply py-1 px-2 h-[36px];
        box-sizing: border-box;
      }

      .p-datepicker {
        @apply rounded-lg shadow-lg;

        .p-datepicker-header {
          @apply py-2 px-3;
        }

        .p-datepicker-calendar {
          th {
            @apply py-1 px-1;
          }

          td {
            @apply py-1 px-1;

            .date-in-range {
              @apply bg-blue-100 text-blue-800 font-medium rounded-sm;
            }
          }
        }

        .p-datepicker-buttonbar {
          @apply border-t border-blue-100 bg-blue-50 p-2;

          button {
            @apply text-blue-600 font-medium py-1 px-2;

            &:hover {
              @apply bg-blue-100 text-blue-800;
            }
          }
        }
      }
    }
  }
}

// Select styling
::ng-deep .p-select {
  @apply h-[36px]; // Fixed height to match input field
  box-sizing: border-box;

  .p-select-label {
    @apply px-2 flex items-center justify-start;
    font-size: 0.8125rem; // Slightly smaller than text-sm for better fit
    line-height: 1; // Adjusted line height for better vertical alignment
    height: 100%; // Make label fill the height of the dropdown
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
  }

  .p-select-label.p-placeholder {
    @apply text-gray-500 flex items-center;
    font-size: 0.8125rem; // Match the size of the regular label
  }

  .p-select-dropdown-icon {
    @apply py-1 px-2;
    height: 100%; // Make trigger fill the height of the dropdown
  }

  // Status dropdown specific styling
  &.status-dropdown {
    .p-select-label {
      @apply flex items-center justify-start;
      padding: 0 0.5rem;
    }
  }
}

// Status dropdown container
.status-dropdown-container {
  @apply w-full;
}

// Status item styling
.status-item, .status-item-selected {
  @apply flex items-center gap-2;
  height: 100%;
}

.status-placeholder {
  @apply text-gray-500;
  font-size: 0.8125rem;
}

.status-indicator {
  @apply inline-block h-2.5 w-2.5 rounded-full;

  &.status-pending {
    @apply bg-yellow-400;
  }

  &.status-approved {
    @apply bg-green-500;
  }

  &.status-rejected {
    @apply bg-red-500;
  }

  &.status-review {
    @apply bg-blue-500;
  }
}

.status-text {
  @apply text-sm font-medium;
  line-height: 1;
}

// Ensure select panel appears above tree view
::ng-deep body .p-select-overlay {
  z-index: 1100 !important; // Same z-index as calendar to ensure consistency
  @apply rounded-xl shadow-lg border border-blue-100;
  animation: dropdownFadeIn 0.2s ease-out;

  .p-select-list {
    @apply p-1;
  }

  .p-select-option {
    @apply rounded-lg py-1.5 px-3 text-blue-800 flex items-center;
    font-size: 0.8125rem; // Match the size of the dropdown label
    height: 30px; // Slightly reduced height for dropdown items
    line-height: 1; // Consistent line height

    &:hover {
      @apply bg-blue-50 text-blue-900;
    }

    &.p-highlight {
      @apply bg-blue-100 text-blue-900 font-medium;
    }

    // Status item in dropdown
    .status-item {
      @apply w-full flex items-center gap-2;
    }
  }
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Input styling
::ng-deep .p-inputtext {
  @apply py-1.5 px-2 h-[36px]; // Fixed height to match dropdown
  font-size: 0.8125rem; // Match the size of the dropdown label
  box-sizing: border-box;
}

// Case ID input styling
.case-id-container {
  @apply w-full;

  .case-id-input {
    @apply w-full py-1.5 px-3 h-[36px] border border-blue-200 rounded-lg;
    transition: all 0.2s ease;

    &:focus {
      @apply border-blue-400 shadow-sm;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
    }

    &:hover:not(:focus) {
      @apply border-blue-300;
    }
  }
}

// Action buttons
.search-form-actions {
  @apply flex items-center gap-2 mt-1.5;
}

.search-submit-btn {
  @apply relative flex items-center px-3 py-1 rounded-lg bg-blue-600 text-white font-medium shadow-sm;
  transition: all 0.2s ease;
  border: none;
  min-width: 90px; // Reduced width for more compact appearance

  &:hover:not(:disabled) {
    @apply bg-blue-500 shadow-md;
  }

  &:active:not(:disabled) {
    @apply bg-blue-700 shadow-inner transform scale-95;
  }

  &:disabled {
    @apply opacity-70 cursor-not-allowed;
  }
}

.search-reset-btn {
  @apply flex items-center px-3 py-1 rounded-lg bg-gray-100 text-gray-700 font-medium shadow-sm;
  transition: all 0.2s ease;
  border: none;

  &:hover:not(:disabled) {
    @apply bg-gray-200;
  }

  &:active:not(:disabled) {
    @apply bg-gray-300 shadow-inner transform scale-95;
  }

  &:disabled {
    @apply opacity-70 cursor-not-allowed;
  }
}

.search-cancel-btn {
  @apply flex items-center px-3 py-1 rounded-lg bg-white text-gray-700 font-medium shadow-sm border border-gray-300;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    @apply bg-gray-50;
  }

  &:active:not(:disabled) {
    @apply bg-gray-100 shadow-inner transform scale-95;
  }

  &:disabled {
    @apply opacity-70 cursor-not-allowed;
  }
}

// Loading spinner
.search-spinner {
  @apply w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}
