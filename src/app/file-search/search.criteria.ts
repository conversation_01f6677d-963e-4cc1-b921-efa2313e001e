export interface ISearchCriteria {
  fromCreationDate?: Date;
  toCreationDate?: Date;
  status?: string;
  caseId?: string;
}

export class SearchCriteria implements ISearchCriteria {
  constructor(
    public readonly fromCreationDate?: Date,
    public readonly toCreationDate?: Date,
    public readonly status?: string,
    public readonly caseId?: string
  ) {}

  static empty(): SearchCriteria {
    return new SearchCriteria();
  }

  static from(criteria: ISearchCriteria): SearchCriteria {
    return new SearchCriteria(
      criteria.fromCreationDate ? new Date(criteria.fromCreationDate) : undefined,
      criteria.toCreationDate ? new Date(criteria.toCreationDate) : undefined,
      criteria.status,
      criteria.caseId
    );
  }

  equals(other: ISearchCriteria): boolean {
    return this.fromCreationDate?.getTime() === other.fromCreationDate?.getTime() &&
      this.toCreationDate?.getTime() === other.toCreationDate?.getTime() &&
      this.status === other.status &&
      this.caseId === other.caseId;
  }

  clone(): SearchCriteria {
    return new SearchCriteria(
      this.fromCreationDate ? new Date(this.fromCreationDate.getTime()) : undefined,
      this.toCreationDate ? new Date(this.toCreationDate.getTime()) : undefined,
      this.status,
      this.caseId
    );
  }

  toString(): string {
    return JSON.stringify({
      fromCreationDate: this.fromCreationDate?.toISOString(),
      toCreationDate: this.toCreationDate?.toISOString(),
      status: this.status,
      caseId: this.caseId
    });
  }

  toObject(): ISearchCriteria {
    return {
      fromCreationDate: this.fromCreationDate,
      toCreationDate: this.toCreationDate,
      status: this.status,
      caseId: this.caseId
    };
  }

  formatted(): string {
    const parts: string[] = [];

    if (this.caseId) {
      parts.push(`Case: ${this.caseId}`);
    }

    if (this.status) {
      parts.push(`Status: ${this.status.charAt(0).toUpperCase() + this.status.slice(1)}`);
    }

    if (this.fromCreationDate || this.toCreationDate) {
      let dateStr = 'Date: ';
      if (this.fromCreationDate && this.toCreationDate) {
        const fromDate = new Date(this.fromCreationDate);
        const toDate = new Date(this.toCreationDate);
        dateStr += `${fromDate.toLocaleDateString()} - ${toDate.toLocaleDateString()}`;
      } else if (this.fromCreationDate) {
        const fromDate = new Date(this.fromCreationDate);
        dateStr += `From ${fromDate.toLocaleDateString()}`;
      } else if (this.toCreationDate) {
        const toDate = new Date(this.toCreationDate);
        dateStr += `Until ${toDate.toLocaleDateString()}`;
      }
      parts.push(dateStr);
    }

    return parts.length > 0 ? parts.join(', ') : 'Search Results';
  }
}
