.search-form-container {
  @apply overflow-hidden bg-gradient-to-br from-blue-50 to-white;
}

.search-form-content {
  @apply p-3;
}

.search-form-header {
  @apply flex items-center justify-between mb-2;
}

.search-form-title {
  @apply text-lg font-bold text-blue-700 flex items-center;
}
.search-form-title i {
  @apply text-blue-500;
}

.search-form-close {
  @apply flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200;
  transition: all 0.2s ease;
  border: none;
}
.search-form-close:hover {
  @apply transform rotate-90;
}

.search-form-grid {
  @apply flex flex-col gap-2;
}

.search-form-compact-layout {
  @apply flex flex-col gap-2;
}

.search-form-row {
  @apply grid grid-cols-2 gap-2;
}

.date-range-group {
  @apply mt-1;
}

.search-form-group {
  @apply flex flex-col;
}

.search-form-label {
  @apply flex items-center text-xs font-medium text-blue-700 mb-0.5;
}
.search-form-label i {
  @apply text-blue-500;
}

.search-input-container {
  @apply relative;
}
.search-input-container.calendar-container {
  @apply w-full;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker {
  @apply w-full h-[36px];
  transition: transform 0.2s ease;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker:hover {
  transform: translateY(-1px);
}
.search-input-container.calendar-container ::ng-deep .p-datepicker.date-range-calendar {
  @apply w-full;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker.date-range-calendar .p-inputtext {
  @apply py-1.5 px-3 h-[36px] border border-blue-200 rounded-lg;
  transition: all 0.2s ease;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker.date-range-calendar .p-inputtext:focus {
  @apply border-blue-400 shadow-sm;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}
.search-input-container.calendar-container ::ng-deep .p-datepicker.date-range-calendar .p-inputtext:hover:not(:focus) {
  @apply border-blue-300;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker.date-range-calendar .p-datepicker-trigger {
  @apply bg-blue-600 border-blue-600 rounded-r-lg;
  transition: all 0.2s ease;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker.date-range-calendar .p-datepicker-trigger:hover {
  @apply bg-blue-500 border-blue-500;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker.date-range-calendar .p-datepicker-trigger:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}
.search-input-container.calendar-container ::ng-deep .p-datepicker.date-range-calendar .p-datepicker-trigger .pi-calendar {
  @apply text-white;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-inputtext {
  @apply py-1.5 px-2 h-[36px];
  font-size: 0.8125rem;
  box-sizing: border-box;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-calendar-clear-icon {
  @apply text-gray-400 hover:text-gray-600;
  position: absolute;
  right: 36px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  cursor: pointer;
  z-index: 1;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-datepicker-trigger {
  @apply py-1 px-2 h-[36px];
  box-sizing: border-box;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-datepicker {
  @apply rounded-lg shadow-lg;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-datepicker .p-datepicker-header {
  @apply py-2 px-3;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-datepicker .p-datepicker-calendar th {
  @apply py-1 px-1;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-datepicker .p-datepicker-calendar td {
  @apply py-1 px-1;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-datepicker .p-datepicker-calendar td .date-in-range {
  @apply bg-blue-100 text-blue-800 font-medium rounded-sm;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-datepicker .p-datepicker-buttonbar {
  @apply border-t border-blue-100 bg-blue-50 p-2;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-datepicker .p-datepicker-buttonbar button {
  @apply text-blue-600 font-medium py-1 px-2;
}
.search-input-container.calendar-container ::ng-deep .p-datepicker .p-datepicker .p-datepicker-buttonbar button:hover {
  @apply bg-blue-100 text-blue-800;
}

::ng-deep .p-select {
  @apply h-[36px];
  box-sizing: border-box;
}
::ng-deep .p-select .p-select-label {
  @apply px-2 flex items-center justify-start;
  font-size: 0.8125rem;
  line-height: 1;
  height: 100%;
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  margin-bottom: 0;
}
::ng-deep .p-select .p-select-label.p-placeholder {
  @apply text-gray-500 flex items-center;
  font-size: 0.8125rem;
}
::ng-deep .p-select .p-select-dropdown-icon {
  @apply py-1 px-2;
  height: 100%;
}
::ng-deep .p-select.status-dropdown .p-select-label {
  @apply flex items-center justify-start;
  padding: 0 0.5rem;
}

.status-dropdown-container {
  @apply w-full;
}

.status-item, .status-item-selected {
  @apply flex items-center gap-2;
  height: 100%;
}

.status-placeholder {
  @apply text-gray-500;
  font-size: 0.8125rem;
}

.status-indicator {
  @apply inline-block h-2.5 w-2.5 rounded-full;
}
.status-indicator.status-pending {
  @apply bg-yellow-400;
}
.status-indicator.status-approved {
  @apply bg-green-500;
}
.status-indicator.status-rejected {
  @apply bg-red-500;
}
.status-indicator.status-review {
  @apply bg-blue-500;
}

.status-text {
  @apply text-sm font-medium;
  line-height: 1;
}

::ng-deep body .p-select-overlay {
  z-index: 1100 !important;
  @apply rounded-xl shadow-lg border border-blue-100;
  animation: dropdownFadeIn 0.2s ease-out;
}
::ng-deep body .p-select-overlay .p-select-list {
  @apply p-1;
}
::ng-deep body .p-select-overlay .p-select-option {
  @apply rounded-lg py-1.5 px-3 text-blue-800 flex items-center;
  font-size: 0.8125rem;
  height: 30px;
  line-height: 1;
}
::ng-deep body .p-select-overlay .p-select-option:hover {
  @apply bg-blue-50 text-blue-900;
}
::ng-deep body .p-select-overlay .p-select-option.p-highlight {
  @apply bg-blue-100 text-blue-900 font-medium;
}
::ng-deep body .p-select-overlay .p-select-option .status-item {
  @apply w-full flex items-center gap-2;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
::ng-deep .p-inputtext {
  @apply py-1.5 px-2 h-[36px];
  font-size: 0.8125rem;
  box-sizing: border-box;
}

.case-id-container {
  @apply w-full;
}
.case-id-container .case-id-input {
  @apply w-full py-1.5 px-3 h-[36px] border border-blue-200 rounded-lg;
  transition: all 0.2s ease;
}
.case-id-container .case-id-input:focus {
  @apply border-blue-400 shadow-sm;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}
.case-id-container .case-id-input:hover:not(:focus) {
  @apply border-blue-300;
}

.search-form-actions {
  @apply flex items-center gap-2 mt-1.5;
}

.search-submit-btn {
  @apply relative flex items-center px-3 py-1 rounded-lg bg-blue-600 text-white font-medium shadow-sm;
  transition: all 0.2s ease;
  border: none;
  min-width: 90px;
}
.search-submit-btn:hover:not(:disabled) {
  @apply bg-blue-500 shadow-md;
}
.search-submit-btn:active:not(:disabled) {
  @apply bg-blue-700 shadow-inner transform scale-95;
}
.search-submit-btn:disabled {
  @apply opacity-70 cursor-not-allowed;
}

.search-reset-btn {
  @apply flex items-center px-3 py-1 rounded-lg bg-gray-100 text-gray-700 font-medium shadow-sm;
  transition: all 0.2s ease;
  border: none;
}
.search-reset-btn:hover:not(:disabled) {
  @apply bg-gray-200;
}
.search-reset-btn:active:not(:disabled) {
  @apply bg-gray-300 shadow-inner transform scale-95;
}
.search-reset-btn:disabled {
  @apply opacity-70 cursor-not-allowed;
}

.search-cancel-btn {
  @apply flex items-center px-3 py-1 rounded-lg bg-white text-gray-700 font-medium shadow-sm border border-gray-300;
  transition: all 0.2s ease;
}
.search-cancel-btn:hover:not(:disabled) {
  @apply bg-gray-50;
}
.search-cancel-btn:active:not(:disabled) {
  @apply bg-gray-100 shadow-inner transform scale-95;
}
.search-cancel-btn:disabled {
  @apply opacity-70 cursor-not-allowed;
}

.search-spinner {
  @apply w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/*# sourceMappingURL=file-search.component.css.map */
