<div class="register-container">
  <!-- Header -->
  <div class="auth-header">
    <h2 class="auth-title">Create Account</h2>
    <p class="auth-subtitle">Join us to start managing your documents</p>
  </div>

  <!-- Error Message -->
  <p-message 
    *ngIf="error" 
    severity="error" 
    [text]="error"
    class="error-message"
    [closable]="true"
    (onClose)="clearError()">
  </p-message>

  <!-- Register Form -->
  <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="auth-form">
    <!-- Username Field -->
    <div class="form-field">
      <label for="username" class="field-label">Username</label>
      <div class="input-wrapper">
        <i class="pi pi-user input-icon"></i>
        <input
          id="username"
          type="text"
          pInputText
          formControlName="username"
          placeholder="Choose a username"
          class="form-input"
          [class.p-invalid]="isFieldInvalid('username')"
          autocomplete="username"
        />
      </div>
      <small 
        *ngIf="getFieldError('username')" 
        class="field-error">
        {{ getFieldError('username') }}
      </small>
    </div>

    <!-- Email Field -->
    <div class="form-field">
      <label for="email" class="field-label">Email Address</label>
      <div class="input-wrapper">
        <i class="pi pi-envelope input-icon"></i>
        <input
          id="email"
          type="email"
          pInputText
          formControlName="email"
          placeholder="Enter your email"
          class="form-input"
          [class.p-invalid]="isFieldInvalid('email')"
          autocomplete="email"
        />
      </div>
      <small 
        *ngIf="getFieldError('email')" 
        class="field-error">
        {{ getFieldError('email') }}
      </small>
    </div>

    <!-- Password Field -->
    <div class="form-field">
      <label for="password" class="field-label">Password</label>
      <div class="input-wrapper">
        <i class="pi pi-lock input-icon"></i>
        <p-password
          id="password"
          formControlName="password"
          placeholder="Create a strong password"
          [toggleMask]="true"
          [feedback]="true"
          styleClass="form-input password-input"
          [inputStyleClass]="isFieldInvalid('password') ? 'p-invalid' : ''"
          autocomplete="new-password">
        </p-password>
      </div>
      <small 
        *ngIf="getFieldError('password')" 
        class="field-error">
        {{ getFieldError('password') }}
      </small>
    </div>

    <!-- Confirm Password Field -->
    <div class="form-field">
      <label for="confirmPassword" class="field-label">Confirm Password</label>
      <div class="input-wrapper">
        <i class="pi pi-lock input-icon"></i>
        <p-password
          id="confirmPassword"
          formControlName="confirmPassword"
          placeholder="Confirm your password"
          [toggleMask]="true"
          [feedback]="false"
          styleClass="form-input password-input"
          [inputStyleClass]="isFieldInvalid('confirmPassword') ? 'p-invalid' : ''"
          autocomplete="new-password">
        </p-password>
      </div>
      <small 
        *ngIf="getFieldError('confirmPassword')" 
        class="field-error">
        {{ getFieldError('confirmPassword') }}
      </small>
    </div>

    <!-- Terms and Conditions -->
    <div class="form-field">
      <div class="terms-wrapper">
        <p-checkbox
          formControlName="acceptTerms"
          inputId="acceptTerms"
          [binary]="true"
          [class.p-invalid]="isFieldInvalid('acceptTerms')">
        </p-checkbox>
        <label for="acceptTerms" class="terms-label">
          I agree to the <a href="#" class="terms-link">Terms of Service</a> 
          and <a href="#" class="terms-link">Privacy Policy</a>
        </label>
      </div>
      <small 
        *ngIf="getFieldError('acceptTerms')" 
        class="field-error">
        {{ getFieldError('acceptTerms') }}
      </small>
    </div>

    <!-- Submit Button -->
    <button
      type="submit"
      pButton
      [disabled]="loading || registerForm.invalid"
      class="submit-button">
      <p-progressSpinner 
        *ngIf="loading" 
        styleClass="spinner-small"
        strokeWidth="4">
      </p-progressSpinner>
      <span *ngIf="!loading">Create Account</span>
      <span *ngIf="loading">Creating Account...</span>
    </button>
  </form>

  <!-- Login Link -->
  <div class="auth-footer">
    <p class="login-prompt">
      Already have an account? 
      <a routerLink="/auth/login" class="login-link">Sign in here</a>
    </p>
  </div>
</div>
