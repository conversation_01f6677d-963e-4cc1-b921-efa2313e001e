.auth-layout {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.auth-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.9) 0%, 
    rgba(147, 51, 234, 0.8) 50%, 
    rgba(239, 68, 68, 0.7) 100%);
}

.pattern-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  background-size: 100px 100px;
}

.auth-container {
  position: relative;
  z-index: 1;
  display: flex;
  min-height: 100vh;
}

.auth-branding {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: white;
}

.brand-content {
  max-width: 500px;
  text-align: center;
}

.logo-section {
  margin-bottom: 3rem;
}

.brand-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.brand-subtitle {
  font-size: 1.25rem;
  opacity: 0.9;
  margin-bottom: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  opacity: 0.95;
}

.feature-item i {
  color: #10b981;
  font-size: 1.25rem;
}

.auth-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-form-wrapper {
  width: 100%;
  max-width: 450px;
  background: white;
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .auth-container {
    flex-direction: column;
  }

  .auth-branding {
    flex: none;
    padding: 1.5rem;
    min-height: 40vh;
  }

  .brand-title {
    font-size: 2rem;
  }

  .brand-subtitle {
    font-size: 1rem;
  }

  .features-list {
    display: none;
  }

  .auth-form-container {
    flex: 1;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(255, 255, 255, 0.1);
  }

  .auth-form-wrapper {
    padding: 2rem;
    margin: 0 1rem;
  }
}

@media (max-width: 480px) {
  .auth-form-wrapper {
    padding: 1.5rem;
    margin: 0 0.5rem;
    border-radius: 16px;
  }

  .brand-title {
    font-size: 1.75rem;
  }
}
