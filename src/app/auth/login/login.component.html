<div class="login-container">
  <!-- Header -->
  <div class="auth-header">
    <h2 class="auth-title">Welcome Back</h2>
    <p class="auth-subtitle">Sign in to your account to continue</p>
  </div>

  <!-- Error Message -->
  <p-message 
    *ngIf="error" 
    severity="error" 
    [text]="error"
    class="error-message"
    [closable]="true"
    (onClose)="clearError()">
  </p-message>

  <!-- Login Form -->
  <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
    <!-- Username Field -->
    <div class="form-field">
      <label for="username" class="field-label">Username</label>
      <div class="input-wrapper">
        <i class="pi pi-user input-icon"></i>
        <input
          id="username"
          type="text"
          pInputText
          formControlName="username"
          placeholder="Enter your username"
          class="form-input"
          [class.p-invalid]="isFieldInvalid('username')"
          autocomplete="username"
        />
      </div>
      <small 
        *ngIf="getFieldError('username')" 
        class="field-error">
        {{ getFieldError('username') }}
      </small>
    </div>

    <!-- Password Field -->
    <div class="form-field">
      <label for="password" class="field-label">Password</label>
      <div class="input-wrapper">
        <i class="pi pi-lock input-icon"></i>
        <p-password
          id="password"
          formControlName="password"
          placeholder="Enter your password"
          [toggleMask]="true"
          [feedback]="false"
          styleClass="form-input password-input"
          [inputStyleClass]="isFieldInvalid('password') ? 'p-invalid' : ''"
          autocomplete="current-password">
        </p-password>
      </div>
      <small 
        *ngIf="getFieldError('password')" 
        class="field-error">
        {{ getFieldError('password') }}
      </small>
    </div>

    <!-- Remember Me & Forgot Password -->
    <div class="form-options">
      <div class="remember-me">
        <p-checkbox
          formControlName="rememberMe"
          inputId="rememberMe"
          [binary]="true">
        </p-checkbox>
        <label for="rememberMe" class="remember-label">Remember me</label>
      </div>
      <a href="#" class="forgot-password">Forgot password?</a>
    </div>

    <!-- Submit Button -->
    <button
      type="submit"
      pButton
      [disabled]="loading || loginForm.invalid"
      class="submit-button">
      <p-progressSpinner 
        *ngIf="loading" 
        styleClass="spinner-small"
        strokeWidth="4">
      </p-progressSpinner>
      <span *ngIf="!loading">Sign In</span>
      <span *ngIf="loading">Signing In...</span>
    </button>
  </form>

  <!-- Register Link -->
  <div class="auth-footer">
    <p class="register-prompt">
      Don't have an account? 
      <a routerLink="/auth/register" class="register-link">Create one here</a>
    </p>
  </div>
</div>
